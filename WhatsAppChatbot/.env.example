# Copy this file to .env and fill in your credentials

# Application Configuration
NODE_ENV=development
PORT=3000
APP_NAME=WhatsApp CRM SaaS
APP_URL=http://localhost:3000

# Database Configuration
DATABASE_URI=mongodb://localhost:27017/whatsapp-crm-saas
DATABASE_URI_TEST=mongodb://localhost:27017/whatsapp-crm-saas-test

# Authentication
JWT_SECRET=your_super_secure_jwt_secret_key_here
JWT_EXPIRES_IN=7d
BCRYPT_ROUNDS=12

# WhatsApp Business API Configuration
WHATSAPP_API_TOKEN=your_whatsapp_business_api_token
WHATSAPP_PHONE_NUMBER_ID=your_phone_number_id
WHATSAPP_BUSINESS_ACCOUNT_ID=your_business_account_id
WHATSAPP_WEBHOOK_VERIFY_TOKEN=your_webhook_verify_token
WHATSAPP_API_VERSION=v17.0

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-3.5-turbo
OPENAI_MAX_TOKENS=150

# Payment Gateway Configuration
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret

# M-Pesa Configuration (Daraja API)
MPESA_CONSUMER_KEY=your_mpesa_consumer_key
MPESA_CONSUMER_SECRET=your_mpesa_consumer_secret
MPESA_SHORTCODE=your_mpesa_shortcode
MPESA_PASSKEY=your_mpesa_passkey
MPESA_ENVIRONMENT=sandbox

# TigoPesa Configuration
TIGOPESA_API_KEY=your_tigopesa_api_key
TIGOPESA_SECRET_KEY=your_tigopesa_secret_key
TIGOPESA_ENVIRONMENT=sandbox

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_email_password

# Redis Configuration (for caching and sessions)
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=

# File Upload Configuration
MAX_FILE_SIZE=5242880
UPLOAD_PATH=uploads/

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Monitoring and Analytics
PROMETHEUS_ENABLED=true
LOG_LEVEL=info
SENTRY_DSN=your_sentry_dsn

# Multi-language Support
DEFAULT_LANGUAGE=en
SUPPORTED_LANGUAGES=en,sw

# Business Configuration
DEFAULT_TIMEZONE=Africa/Dar_es_Salaam
CURRENCY=TZS