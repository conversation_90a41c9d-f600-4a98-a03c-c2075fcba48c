# AI Integration Guide - WhatsApp CRM SaaS

## Overview
This guide covers the AI-powered features integrated into the WhatsApp CRM SaaS platform using OpenRouter API, providing intelligent automation and insights for East African SMEs.

## AI Features Implemented

### 1. Intelligent Response Suggestions
**Endpoint**: `POST /api/v1/ai/suggestions/:conversationId`

Generates contextual response suggestions for customer service agents based on:
- Conversation history and context
- Customer profile and journey stage
- Business type and industry
- Cultural context for East African communication

**Features**:
- 3 response types: Empathetic, Solution-focused, Follow-up question
- Multi-language support (English/Swahili)
- Business context awareness
- Confidence scoring

### 2. Sentiment Analysis
**Endpoint**: `POST /api/v1/ai/sentiment`

Analyzes customer message sentiment with cultural context for East African communication styles.

**Features**:
- Real-time sentiment scoring (-1.0 to 1.0)
- Cultural context awareness
- Confidence levels
- Automatic language detection
- Integration with conversation analytics

### 3. Context-Aware Chatbot Responses
**Endpoint**: `POST /api/v1/ai/chatbot-response`

Generates intelligent automated responses based on:
- Conversation context and history
- Customer profile and preferences
- Business hours and settings
- Industry-specific knowledge

**Features**:
- Business hours awareness
- Personalized responses using customer name
- Industry-specific language and tone
- Multi-language support
- Fallback responses for AI failures

### 4. Contact Insights & Recommendations
**Endpoint**: `GET /api/v1/ai/contact-insights/:contactId`

Provides AI-powered customer insights including:
- Engagement level assessment
- Risk level evaluation
- Next best action recommendations
- Upselling opportunities
- Customer lifetime value predictions

### 5. Language Detection
**Endpoint**: `POST /api/v1/ai/detect-language`

Automatically detects message language for:
- Proper response language selection
- Contact language preference updates
- Analytics and reporting
- Automation rule targeting

### 6. Smart Routing & Escalation
Automatically routes conversations based on:
- Sentiment analysis (negative → escalate)
- Content analysis and intent detection
- Customer priority and history
- Agent availability and expertise

## Configuration

### Environment Variables
```bash
# OpenRouter API Configuration
OPENROUTER_API_KEY=your_openrouter_api_key
OPENROUTER_BASE_URL=https://openrouter.ai/api/v1
OPENROUTER_MODEL=anthropic/claude-3-haiku
OPENROUTER_FALLBACK_MODEL=openai/gpt-3.5-turbo

# AI Settings
AI_MAX_TOKENS=500
AI_TEMPERATURE=0.7
AI_CONTEXT_WINDOW=4000
```

### Business Settings
Enable AI features in business settings:
```javascript
{
  "settings": {
    "aiAutomation": {
      "enabled": true,
      "responseGeneration": true,
      "sentimentAnalysis": true,
      "smartRouting": true,
      "languageDetection": true
    }
  }
}
```

## API Usage Examples

### Generate Response Suggestions
```javascript
// GET /api/v1/conversations/:conversationId/ai-suggestions
{
  "suggestions": [
    {
      "text": "I understand your concern and I'm here to help resolve this issue.",
      "type": "empathetic"
    },
    {
      "text": "Let me check your account details and provide a solution right away.",
      "type": "solution"
    },
    {
      "text": "Could you please provide more details about when this issue started?",
      "type": "question"
    }
  ],
  "confidence": 0.85,
  "model": "anthropic/claude-3-haiku"
}
```

### Analyze Sentiment
```javascript
// POST /api/v1/ai/sentiment
{
  "text": "I'm very disappointed with the service quality",
  "language": "en"
}

// Response
{
  "success": true,
  "sentiment": "negative",
  "score": -0.7,
  "confidence": 0.9,
  "reasoning": "Customer expresses disappointment and dissatisfaction"
}
```

### Generate Contact Insights
```javascript
// GET /api/v1/ai/contact-insights/:contactId
{
  "success": true,
  "insights": {
    "summary": "High-value customer with consistent engagement",
    "engagementLevel": "high",
    "riskLevel": "low",
    "recommendations": [
      "Offer premium service upgrade",
      "Schedule quarterly check-in",
      "Request referrals"
    ],
    "nextBestAction": "upsell_opportunity",
    "opportunities": [
      "Premium service upgrade",
      "Referral program enrollment"
    ]
  }
}
```

## AI Automation Types

### 1. AI Response Automation
```javascript
{
  "type": "ai_response",
  "triggers": {
    "keywords": [
      { "text": "help", "matchType": "contains" },
      { "text": "support", "matchType": "contains" }
    ]
  },
  "actions": [
    {
      "type": "generate_ai_response",
      "config": {
        "useBusinessContext": true,
        "respectBusinessHours": true,
        "maxTokens": 200
      }
    }
  ]
}
```

### 2. Sentiment-Based Routing
```javascript
{
  "type": "sentiment_trigger",
  "triggers": {
    "conditions": [
      {
        "field": "sentiment",
        "operator": "less_than",
        "value": -0.5
      }
    ]
  },
  "actions": [
    {
      "type": "escalate",
      "config": {
        "priority": "urgent",
        "department": "customer_service",
        "notifyManager": true
      }
    }
  ]
}
```

### 3. Smart Routing
```javascript
{
  "type": "smart_routing",
  "triggers": {
    "events": ["message_received"]
  },
  "actions": [
    {
      "type": "analyze_and_route",
      "config": {
        "departments": {
          "sales": ["purchase", "buy", "price", "cost"],
          "support": ["problem", "issue", "help", "broken"],
          "billing": ["payment", "invoice", "bill", "charge"]
        }
      }
    }
  ]
}
```

## Performance & Monitoring

### AI Usage Tracking
Monitor AI service usage through:
- Request counts by type
- Average confidence scores
- Cost estimation
- Model performance metrics
- Error rates and fallback usage

### Rate Limiting
- Respect OpenRouter API rate limits
- Implement exponential backoff
- Use fallback models for high availability
- Cache frequent responses

### Cost Optimization
- Use appropriate models for each task
- Implement response caching
- Optimize prompt engineering
- Monitor token usage

## Best Practices

### 1. Prompt Engineering
- Use clear, specific instructions
- Include cultural context for East African markets
- Provide examples for consistent outputs
- Test prompts with various inputs

### 2. Error Handling
- Always provide fallback responses
- Log AI failures for analysis
- Graceful degradation when AI is unavailable
- User-friendly error messages

### 3. Privacy & Security
- Don't log sensitive customer data
- Implement data retention policies
- Use secure API connections
- Comply with data protection regulations

### 4. Cultural Sensitivity
- Train models on East African communication styles
- Respect local business practices
- Use appropriate language and tone
- Consider cultural holidays and events

## Troubleshooting

### Common Issues
1. **API Key Invalid**: Check OpenRouter API key configuration
2. **Rate Limit Exceeded**: Implement proper rate limiting and backoff
3. **Model Unavailable**: Ensure fallback model is configured
4. **Low Confidence Scores**: Review and optimize prompts
5. **Language Detection Errors**: Validate input text length and quality

### Testing AI Features
Use the test endpoint to verify AI service connectivity:
```bash
POST /api/v1/ai/test
```

### Monitoring
- Check AI usage statistics regularly
- Monitor response quality and user feedback
- Track automation success rates
- Review cost vs. value metrics

## Future Enhancements
- Voice message transcription and analysis
- Image recognition for product inquiries
- Predictive customer behavior modeling
- Advanced conversation summarization
- Multi-modal AI interactions
