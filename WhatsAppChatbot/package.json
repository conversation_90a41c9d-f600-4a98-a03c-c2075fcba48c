{"name": "whatsapp-crm-saas", "version": "2.0.0", "description": "WhatsApp-based CRM SaaS platform for East African SMEs", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "migrate": "node src/scripts/migrate.js", "seed": "node src/scripts/seed.js"}, "dependencies": {"express": "^4.18.2", "mongoose": "^7.5.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "helmet": "^7.0.0", "express-rate-limit": "^6.10.0", "express-validator": "^7.0.1", "prom-client": "^14.2.0", "winston": "^3.10.0", "axios": "^1.5.0", "node-cron": "^3.0.2", "multer": "^1.4.5-lts.1", "sharp": "^0.32.5", "i18next": "^23.4.4", "i18next-fs-backend": "^2.1.5", "openai": "^4.0.0", "stripe": "^13.5.0", "@anthropic-ai/sdk": "^0.24.3"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.4", "supertest": "^6.3.3", "@types/jest": "^29.5.5", "eslint": "^8.48.0", "prettier": "^3.0.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["whatsapp", "crm", "saas", "tanzania", "east-africa", "sme", "business", "automation"], "author": "Your Name", "license": "MIT"}