const dotenv = require('dotenv');

dotenv.config();

const config = {
    // Application Configuration
    app: {
        name: process.env.APP_NAME || 'WhatsApp CRM SaaS',
        env: process.env.NODE_ENV || 'development',
        port: parseInt(process.env.PORT) || 3000,
        url: process.env.APP_URL || 'http://localhost:3000',
    },

    // Database Configuration
    database: {
        uri: process.env.DATABASE_URI || 'mongodb://localhost:27017/whatsapp-crm-saas',
        testUri: process.env.DATABASE_URI_TEST || 'mongodb://localhost:27017/whatsapp-crm-saas-test',
        options: {
            useNewUrlParser: true,
            useUnifiedTopology: true,
        },
    },

    // Authentication Configuration
    auth: {
        jwtSecret: process.env.JWT_SECRET || 'fallback-secret-key',
        jwtExpiresIn: process.env.JWT_EXPIRES_IN || '7d',
        bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS) || 12,
    },

    // WhatsApp Business API Configuration
    whatsapp: {
        apiToken: process.env.WHATSAPP_API_TOKEN,
        phoneNumberId: process.env.WHATSAPP_PHONE_NUMBER_ID,
        businessAccountId: process.env.WHATSAPP_BUSINESS_ACCOUNT_ID,
        webhookVerifyToken: process.env.WHATSAPP_WEBHOOK_VERIFY_TOKEN,
        apiVersion: process.env.WHATSAPP_API_VERSION || 'v17.0',
        baseUrl: 'https://graph.facebook.com',
    },

    // OpenAI Configuration
    openai: {
        apiKey: process.env.OPENAI_API_KEY,
        model: process.env.OPENAI_MODEL || 'gpt-3.5-turbo',
        maxTokens: parseInt(process.env.OPENAI_MAX_TOKENS) || 150,
    },

    // Payment Configuration
    payments: {
        stripe: {
            secretKey: process.env.STRIPE_SECRET_KEY,
            publishableKey: process.env.STRIPE_PUBLISHABLE_KEY,
            webhookSecret: process.env.STRIPE_WEBHOOK_SECRET,
        },
        mpesa: {
            consumerKey: process.env.MPESA_CONSUMER_KEY,
            consumerSecret: process.env.MPESA_CONSUMER_SECRET,
            shortcode: process.env.MPESA_SHORTCODE,
            passkey: process.env.MPESA_PASSKEY,
            environment: process.env.MPESA_ENVIRONMENT || 'sandbox',
        },
        tigopesa: {
            apiKey: process.env.TIGOPESA_API_KEY,
            secretKey: process.env.TIGOPESA_SECRET_KEY,
            environment: process.env.TIGOPESA_ENVIRONMENT || 'sandbox',
        },
    },

    // Email Configuration
    email: {
        host: process.env.SMTP_HOST || 'smtp.gmail.com',
        port: parseInt(process.env.SMTP_PORT) || 587,
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS,
    },

    // Redis Configuration
    redis: {
        url: process.env.REDIS_URL || 'redis://localhost:6379',
        password: process.env.REDIS_PASSWORD,
    },

    // File Upload Configuration
    upload: {
        maxFileSize: parseInt(process.env.MAX_FILE_SIZE) || 5242880, // 5MB
        uploadPath: process.env.UPLOAD_PATH || 'uploads/',
    },

    // Rate Limiting Configuration
    rateLimit: {
        windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 900000, // 15 minutes
        maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,
    },

    // Monitoring Configuration
    monitoring: {
        prometheusEnabled: process.env.PROMETHEUS_ENABLED === 'true',
        logLevel: process.env.LOG_LEVEL || 'info',
        sentryDsn: process.env.SENTRY_DSN,
    },

    // Internationalization Configuration
    i18n: {
        defaultLanguage: process.env.DEFAULT_LANGUAGE || 'en',
        supportedLanguages: (process.env.SUPPORTED_LANGUAGES || 'en,sw').split(','),
    },

    // Business Configuration
    business: {
        defaultTimezone: process.env.DEFAULT_TIMEZONE || 'Africa/Dar_es_Salaam',
        currency: process.env.CURRENCY || 'TZS',
    },
};

// Validation for required environment variables
const requiredEnvVars = [
    'JWT_SECRET',
    'DATABASE_URI',
];

const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);

if (missingEnvVars.length > 0 && config.app.env === 'production') {
    console.error('Missing required environment variables:', missingEnvVars);
    process.exit(1);
}

module.exports = config;