const { validationResult } = require('express-validator');
const { Conversation, Contact, Business } = require('../models');
const aiService = require('../services/aiService');
const logger = require('../services/logger');

// Generate response suggestions for agents
const generateResponseSuggestions = async (req, res) => {
    try {
        const { conversationId } = req.params;
        const businessId = req.business._id;

        const conversation = await Conversation.findOne({
            _id: conversationId,
            business: businessId,
        }).populate('contact');

        if (!conversation) {
            return res.status(404).json({
                error: 'Conversation not found',
            });
        }

        const business = await Business.findById(businessId);
        const contact = conversation.contact;

        // Generate AI suggestions
        const result = await aiService.generateResponseSuggestions(
            conversation,
            contact,
            {
                name: business.name,
                industry: business.industry,
                settings: business.settings
            }
        );

        if (!result.success) {
            return res.status(500).json({
                error: 'Failed to generate suggestions',
                details: result.error,
            });
        }

        logger.business.userAction(req.user.id, businessId, 'ai_suggestions_generated', {
            conversationId,
            suggestionsCount: result.suggestions.length,
        });

        res.json({
            suggestions: result.suggestions,
            confidence: result.confidence,
            model: result.model,
        });
    } catch (error) {
        logger.business.apiError(req.business._id, 'POST /ai/suggestions', error);
        res.status(500).json({
            error: 'Failed to generate response suggestions',
        });
    }
};

// Analyze sentiment of a message
const analyzeSentiment = async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                error: 'Validation failed',
                details: errors.array(),
            });
        }

        const { text, language = 'en' } = req.body;
        const businessId = req.business._id;

        const result = await aiService.analyzeSentiment(text, language);

        logger.business.userAction(req.user.id, businessId, 'sentiment_analyzed', {
            sentiment: result.sentiment,
            confidence: result.confidence,
        });

        res.json(result);
    } catch (error) {
        logger.business.apiError(req.business._id, 'POST /ai/sentiment', error);
        res.status(500).json({
            error: 'Failed to analyze sentiment',
        });
    }
};

// Generate chatbot response
const generateChatbotResponse = async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                error: 'Validation failed',
                details: errors.array(),
            });
        }

        const { conversationId, message } = req.body;
        const businessId = req.business._id;

        const conversation = await Conversation.findOne({
            _id: conversationId,
            business: businessId,
        }).populate('contact');

        if (!conversation) {
            return res.status(404).json({
                error: 'Conversation not found',
            });
        }

        const business = await Business.findById(businessId);
        const contact = conversation.contact;

        const result = await aiService.generateChatbotResponse(
            message,
            contact,
            conversation,
            {
                name: business.name,
                industry: business.industry,
                settings: business.settings
            }
        );

        logger.business.userAction(req.user.id, businessId, 'chatbot_response_generated', {
            conversationId,
            confidence: result.confidence,
        });

        res.json(result);
    } catch (error) {
        logger.business.apiError(req.business._id, 'POST /ai/chatbot-response', error);
        res.status(500).json({
            error: 'Failed to generate chatbot response',
        });
    }
};

// Generate contact insights
const generateContactInsights = async (req, res) => {
    try {
        const { contactId } = req.params;
        const businessId = req.business._id;

        const contact = await Contact.findOne({
            _id: contactId,
            business: businessId,
        });

        if (!contact) {
            return res.status(404).json({
                error: 'Contact not found',
            });
        }

        // Get recent conversations for this contact
        const conversations = await Conversation.find({
            business: businessId,
            contact: contactId,
        })
            .sort({ createdAt: -1 })
            .limit(10)
            .lean();

        const business = await Business.findById(businessId);

        const result = await aiService.generateContactInsights(
            contact,
            conversations,
            {
                name: business.name,
                industry: business.industry,
                settings: business.settings
            }
        );

        logger.business.userAction(req.user.id, businessId, 'contact_insights_generated', {
            contactId,
            riskLevel: result.riskLevel,
        });

        res.json(result);
    } catch (error) {
        logger.business.apiError(req.business._id, 'GET /ai/contact-insights/:id', error);
        res.status(500).json({
            error: 'Failed to generate contact insights',
        });
    }
};

// Detect language of text
const detectLanguage = async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                error: 'Validation failed',
                details: errors.array(),
            });
        }

        const { text } = req.body;
        const businessId = req.business._id;

        const result = await aiService.detectLanguage(text);

        logger.business.userAction(req.user.id, businessId, 'language_detected', {
            detectedLanguage: result.language,
            confidence: result.confidence,
        });

        res.json(result);
    } catch (error) {
        logger.business.apiError(req.business._id, 'POST /ai/detect-language', error);
        res.status(500).json({
            error: 'Failed to detect language',
        });
    }
};

// Bulk analyze conversations for insights
const bulkAnalyzeConversations = async (req, res) => {
    try {
        const { conversationIds, analysisType = 'sentiment' } = req.body;
        const businessId = req.business._id;

        if (!conversationIds || !Array.isArray(conversationIds)) {
            return res.status(400).json({
                error: 'conversationIds array is required',
            });
        }

        const conversations = await Conversation.find({
            _id: { $in: conversationIds },
            business: businessId,
        }).populate('contact');

        const results = [];

        for (const conversation of conversations) {
            try {
                const lastMessage = conversation.messages
                    .filter(msg => msg.direction === 'inbound')
                    .pop();

                if (!lastMessage || !lastMessage.content.text) {
                    continue;
                }

                let analysisResult;
                
                switch (analysisType) {
                    case 'sentiment':
                        analysisResult = await aiService.analyzeSentiment(
                            lastMessage.content.text,
                            conversation.contact.language || 'en'
                        );
                        break;
                    
                    case 'language':
                        analysisResult = await aiService.detectLanguage(
                            lastMessage.content.text
                        );
                        break;
                    
                    default:
                        continue;
                }

                results.push({
                    conversationId: conversation._id,
                    contactId: conversation.contact._id,
                    analysis: analysisResult,
                });

                // Add small delay to respect rate limits
                await new Promise(resolve => setTimeout(resolve, 100));

            } catch (error) {
                logger.error('Error analyzing conversation:', error);
                results.push({
                    conversationId: conversation._id,
                    error: error.message,
                });
            }
        }

        logger.business.userAction(req.user.id, businessId, 'bulk_analysis_completed', {
            analysisType,
            conversationCount: conversationIds.length,
            successCount: results.filter(r => !r.error).length,
        });

        res.json({
            results,
            summary: {
                total: conversationIds.length,
                analyzed: results.filter(r => !r.error).length,
                failed: results.filter(r => r.error).length,
            },
        });
    } catch (error) {
        logger.business.apiError(req.business._id, 'POST /ai/bulk-analyze', error);
        res.status(500).json({
            error: 'Failed to perform bulk analysis',
        });
    }
};

// Get AI usage statistics for the business
const getAIUsageStats = async (req, res) => {
    try {
        const businessId = req.business._id;
        const { period = '30d' } = req.query;

        // Calculate date range
        const endDate = new Date();
        const startDate = new Date();
        
        switch (period) {
            case '7d':
                startDate.setDate(endDate.getDate() - 7);
                break;
            case '30d':
                startDate.setDate(endDate.getDate() - 30);
                break;
            case '90d':
                startDate.setDate(endDate.getDate() - 90);
                break;
            default:
                startDate.setDate(endDate.getDate() - 30);
        }

        // This would typically come from a usage tracking collection
        // For now, we'll return mock data structure
        const usageStats = {
            period: { start: startDate, end: endDate },
            totalRequests: 0,
            requestsByType: {
                suggestions: 0,
                sentiment: 0,
                chatbot: 0,
                insights: 0,
                language: 0,
            },
            averageConfidence: 0,
            costEstimate: 0,
            topModelsUsed: [],
        };

        res.json(usageStats);
    } catch (error) {
        logger.business.apiError(req.business._id, 'GET /ai/usage-stats', error);
        res.status(500).json({
            error: 'Failed to get AI usage statistics',
        });
    }
};

// Test AI service connectivity
const testAIService = async (req, res) => {
    try {
        const businessId = req.business._id;

        // Test with a simple sentiment analysis
        const testResult = await aiService.analyzeSentiment(
            'This is a test message to verify AI service connectivity.',
            'en'
        );

        logger.business.userAction(req.user.id, businessId, 'ai_service_tested', {
            success: testResult.success,
        });

        res.json({
            status: testResult.success ? 'connected' : 'failed',
            testResult,
            timestamp: new Date().toISOString(),
        });
    } catch (error) {
        logger.business.apiError(req.business._id, 'POST /ai/test', error);
        res.status(500).json({
            error: 'AI service test failed',
            details: error.message,
        });
    }
};

module.exports = {
    generateResponseSuggestions,
    analyzeSentiment,
    generateChatbotResponse,
    generateContactInsights,
    detectLanguage,
    bulkAnalyzeConversations,
    getAIUsageStats,
    testAIService,
};
