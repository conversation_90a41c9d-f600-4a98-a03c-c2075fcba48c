const { Contact, Conversation, Campaign, Automation, Business } = require('../models');
const logger = require('../services/logger');

// Get business overview analytics
const getBusinessOverview = async (req, res) => {
    try {
        const businessId = req.business._id;
        const { period = '30d' } = req.query;

        // Calculate date range
        const endDate = new Date();
        const startDate = new Date();
        
        switch (period) {
            case '7d':
                startDate.setDate(endDate.getDate() - 7);
                break;
            case '30d':
                startDate.setDate(endDate.getDate() - 30);
                break;
            case '90d':
                startDate.setDate(endDate.getDate() - 90);
                break;
            case '1y':
                startDate.setFullYear(endDate.getFullYear() - 1);
                break;
            default:
                startDate.setDate(endDate.getDate() - 30);
        }

        // Get basic counts
        const [
            totalContacts,
            activeContacts,
            totalConversations,
            openConversations,
            totalCampaigns,
            activeCampaigns,
            totalAutomations,
            activeAutomations
        ] = await Promise.all([
            Contact.countDocuments({ business: businessId }),
            Contact.countDocuments({ business: businessId, status: 'active' }),
            Conversation.countDocuments({ business: businessId }),
            Conversation.countDocuments({ business: businessId, status: 'open' }),
            Campaign.countDocuments({ business: businessId }),
            Campaign.countDocuments({ business: businessId, status: { $in: ['running', 'scheduled'] } }),
            Automation.countDocuments({ business: businessId }),
            Automation.countDocuments({ business: businessId, status: 'active' }),
        ]);

        // Get period-specific metrics
        const periodFilter = {
            business: businessId,
            createdAt: { $gte: startDate, $lte: endDate }
        };

        const [
            newContacts,
            newConversations,
            completedConversations,
            campaignsSent,
            automationsTriggered
        ] = await Promise.all([
            Contact.countDocuments(periodFilter),
            Conversation.countDocuments(periodFilter),
            Conversation.countDocuments({
                ...periodFilter,
                status: 'closed'
            }),
            Campaign.aggregate([
                { $match: { business: businessId, 'execution.startedAt': { $gte: startDate, $lte: endDate } } },
                { $group: { _id: null, totalSent: { $sum: '$analytics.sent' } } }
            ]),
            Automation.aggregate([
                { $match: { business: businessId } },
                { $unwind: '$executions' },
                { $match: { 'executions.triggeredAt': { $gte: startDate, $lte: endDate } } },
                { $count: 'total' }
            ])
        ]);

        // Calculate response metrics
        const responseMetrics = await Conversation.aggregate([
            { $match: periodFilter },
            {
                $group: {
                    _id: null,
                    avgFirstResponseTime: { $avg: '$metrics.firstResponseTime' },
                    avgResolutionTime: { $avg: '$metrics.resolutionTime' },
                    totalMessages: { $sum: '$metrics.messageCount' }
                }
            }
        ]);

        // Get engagement metrics
        const engagementMetrics = await Contact.aggregate([
            { $match: { business: businessId } },
            {
                $group: {
                    _id: null,
                    avgEngagementScore: { $avg: '$engagement.score' },
                    totalMessages: { $sum: '$totalMessages' }
                }
            }
        ]);

        res.json({
            overview: {
                contacts: {
                    total: totalContacts,
                    active: activeContacts,
                    new: newContacts,
                    activeRate: totalContacts > 0 ? ((activeContacts / totalContacts) * 100).toFixed(1) : 0
                },
                conversations: {
                    total: totalConversations,
                    open: openConversations,
                    new: newConversations,
                    completed: completedConversations,
                    resolutionRate: newConversations > 0 ? ((completedConversations / newConversations) * 100).toFixed(1) : 0
                },
                campaigns: {
                    total: totalCampaigns,
                    active: activeCampaigns,
                    messagesSent: campaignsSent[0]?.totalSent || 0
                },
                automations: {
                    total: totalAutomations,
                    active: activeAutomations,
                    triggered: automationsTriggered[0]?.total || 0
                },
                performance: {
                    avgFirstResponseTime: responseMetrics[0]?.avgFirstResponseTime || 0,
                    avgResolutionTime: responseMetrics[0]?.avgResolutionTime || 0,
                    avgEngagementScore: engagementMetrics[0]?.avgEngagementScore || 0,
                    totalMessages: (responseMetrics[0]?.totalMessages || 0) + (engagementMetrics[0]?.totalMessages || 0)
                }
            },
            period: {
                start: startDate,
                end: endDate,
                label: period
            }
        });
    } catch (error) {
        logger.business.apiError(req.business._id, 'GET /analytics/overview', error);
        res.status(500).json({
            error: 'Failed to fetch business overview',
        });
    }
};

// Get message analytics
const getMessageAnalytics = async (req, res) => {
    try {
        const businessId = req.business._id;
        const { period = '30d', groupBy = 'day' } = req.query;

        // Calculate date range
        const endDate = new Date();
        const startDate = new Date();
        
        switch (period) {
            case '7d':
                startDate.setDate(endDate.getDate() - 7);
                break;
            case '30d':
                startDate.setDate(endDate.getDate() - 30);
                break;
            case '90d':
                startDate.setDate(endDate.getDate() - 90);
                break;
            default:
                startDate.setDate(endDate.getDate() - 30);
        }

        // Define grouping format
        let dateFormat;
        switch (groupBy) {
            case 'hour':
                dateFormat = { $dateToString: { format: "%Y-%m-%d %H:00", date: "$createdAt" } };
                break;
            case 'day':
                dateFormat = { $dateToString: { format: "%Y-%m-%d", date: "$createdAt" } };
                break;
            case 'week':
                dateFormat = { $dateToString: { format: "%Y-W%U", date: "$createdAt" } };
                break;
            case 'month':
                dateFormat = { $dateToString: { format: "%Y-%m", date: "$createdAt" } };
                break;
            default:
                dateFormat = { $dateToString: { format: "%Y-%m-%d", date: "$createdAt" } };
        }

        // Get message volume over time
        const messageVolume = await Conversation.aggregate([
            { $match: { business: businessId, createdAt: { $gte: startDate, $lte: endDate } } },
            { $unwind: '$messages' },
            {
                $group: {
                    _id: {
                        date: dateFormat,
                        direction: '$messages.direction'
                    },
                    count: { $sum: 1 }
                }
            },
            { $sort: { '_id.date': 1 } }
        ]);

        // Get message types breakdown
        const messageTypes = await Conversation.aggregate([
            { $match: { business: businessId, createdAt: { $gte: startDate, $lte: endDate } } },
            { $unwind: '$messages' },
            {
                $group: {
                    _id: '$messages.type',
                    count: { $sum: 1 }
                }
            },
            { $sort: { count: -1 } }
        ]);

        // Get response time trends
        const responseTimes = await Conversation.aggregate([
            { $match: { business: businessId, createdAt: { $gte: startDate, $lte: endDate } } },
            {
                $group: {
                    _id: dateFormat,
                    avgFirstResponseTime: { $avg: '$metrics.firstResponseTime' },
                    avgResolutionTime: { $avg: '$metrics.resolutionTime' },
                    count: { $sum: 1 }
                }
            },
            { $sort: { '_id': 1 } }
        ]);

        res.json({
            messageVolume: messageVolume.reduce((acc, item) => {
                const date = item._id.date;
                if (!acc[date]) acc[date] = { inbound: 0, outbound: 0 };
                acc[date][item._id.direction] = item.count;
                return acc;
            }, {}),
            messageTypes: messageTypes.map(item => ({
                type: item._id,
                count: item.count
            })),
            responseTimes: responseTimes.map(item => ({
                date: item._id,
                avgFirstResponseTime: Math.round(item.avgFirstResponseTime || 0),
                avgResolutionTime: Math.round(item.avgResolutionTime || 0),
                conversationCount: item.count
            })),
            period: { start: startDate, end: endDate, groupBy }
        });
    } catch (error) {
        logger.business.apiError(req.business._id, 'GET /analytics/messages', error);
        res.status(500).json({
            error: 'Failed to fetch message analytics',
        });
    }
};

// Get contact analytics
const getContactAnalytics = async (req, res) => {
    try {
        const businessId = req.business._id;

        // Get contact growth over time
        const contactGrowth = await Contact.aggregate([
            { $match: { business: businessId } },
            {
                $group: {
                    _id: { $dateToString: { format: "%Y-%m", date: "$createdAt" } },
                    newContacts: { $sum: 1 }
                }
            },
            { $sort: { '_id': 1 } }
        ]);

        // Get contact sources
        const contactSources = await Contact.aggregate([
            { $match: { business: businessId } },
            {
                $group: {
                    _id: '$source',
                    count: { $sum: 1 }
                }
            }
        ]);

        // Get engagement distribution
        const engagementDistribution = await Contact.aggregate([
            { $match: { business: businessId } },
            {
                $bucket: {
                    groupBy: '$engagement.score',
                    boundaries: [0, 20, 40, 60, 80, 100],
                    default: 'unknown',
                    output: {
                        count: { $sum: 1 }
                    }
                }
            }
        ]);

        // Get journey stage distribution
        const journeyStages = await Contact.aggregate([
            { $match: { business: businessId } },
            {
                $group: {
                    _id: '$journey.stage',
                    count: { $sum: 1 },
                    avgLifetimeValue: { $avg: '$journey.lifetimeValue' }
                }
            }
        ]);

        // Get top tags
        const topTags = await Contact.aggregate([
            { $match: { business: businessId } },
            { $unwind: '$tags' },
            {
                $group: {
                    _id: '$tags',
                    count: { $sum: 1 }
                }
            },
            { $sort: { count: -1 } },
            { $limit: 10 }
        ]);

        res.json({
            contactGrowth: contactGrowth.map(item => ({
                month: item._id,
                newContacts: item.newContacts
            })),
            contactSources: contactSources.map(item => ({
                source: item._id,
                count: item.count
            })),
            engagementDistribution: engagementDistribution.map(item => ({
                range: item._id === 'unknown' ? 'Unknown' : `${item._id}-${item._id + 19}`,
                count: item.count
            })),
            journeyStages: journeyStages.map(item => ({
                stage: item._id,
                count: item.count,
                avgLifetimeValue: Math.round(item.avgLifetimeValue || 0)
            })),
            topTags: topTags.map(item => ({
                tag: item._id,
                count: item.count
            }))
        });
    } catch (error) {
        logger.business.apiError(req.business._id, 'GET /analytics/contacts', error);
        res.status(500).json({
            error: 'Failed to fetch contact analytics',
        });
    }
};

// Get campaign performance analytics
const getCampaignAnalytics = async (req, res) => {
    try {
        const businessId = req.business._id;

        // Get campaign performance summary
        const campaignSummary = await Campaign.aggregate([
            { $match: { business: businessId } },
            {
                $group: {
                    _id: '$status',
                    count: { $sum: 1 },
                    totalSent: { $sum: '$analytics.sent' },
                    totalDelivered: { $sum: '$analytics.delivered' },
                    totalRead: { $sum: '$analytics.read' },
                    totalReplies: { $sum: '$analytics.replies' }
                }
            }
        ]);

        // Get campaign performance by type
        const campaignsByType = await Campaign.aggregate([
            { $match: { business: businessId } },
            {
                $group: {
                    _id: '$type',
                    count: { $sum: 1 },
                    avgDeliveryRate: { $avg: '$analytics.deliveryRate' },
                    avgOpenRate: { $avg: '$analytics.openRate' },
                    avgReplyRate: { $avg: '$analytics.replyRate' }
                }
            }
        ]);

        // Get recent campaign performance
        const recentCampaigns = await Campaign.find({ business: businessId })
            .sort({ 'execution.startedAt': -1 })
            .limit(10)
            .select('name type status analytics execution')
            .lean();

        res.json({
            summary: campaignSummary.reduce((acc, item) => {
                acc[item._id] = {
                    count: item.count,
                    totalSent: item.totalSent,
                    totalDelivered: item.totalDelivered,
                    totalRead: item.totalRead,
                    totalReplies: item.totalReplies
                };
                return acc;
            }, {}),
            performanceByType: campaignsByType.map(item => ({
                type: item._id,
                count: item.count,
                avgDeliveryRate: Math.round(item.avgDeliveryRate || 0),
                avgOpenRate: Math.round(item.avgOpenRate || 0),
                avgReplyRate: Math.round(item.avgReplyRate || 0)
            })),
            recentCampaigns: recentCampaigns.map(campaign => ({
                id: campaign._id,
                name: campaign.name,
                type: campaign.type,
                status: campaign.status,
                sent: campaign.analytics.sent,
                deliveryRate: campaign.analytics.deliveryRate,
                openRate: campaign.analytics.openRate,
                replyRate: campaign.analytics.replyRate,
                startedAt: campaign.execution.startedAt
            }))
        });
    } catch (error) {
        logger.business.apiError(req.business._id, 'GET /analytics/campaigns', error);
        res.status(500).json({
            error: 'Failed to fetch campaign analytics',
        });
    }
};

module.exports = {
    getBusinessOverview,
    getMessageAnalytics,
    getContactAnalytics,
    getCampaignAnalytics,
};
