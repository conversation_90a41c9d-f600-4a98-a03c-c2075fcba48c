const bcrypt = require('bcryptjs');
const { validationResult } = require('express-validator');
const { User, Business } = require('../models');
const { generateToken } = require('../middleware/auth');
const logger = require('../services/logger');
const config = require('../config');

// Register new user
const register = async (req, res) => {
    try {
        // Check for validation errors
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                error: 'Validation failed',
                details: errors.array(),
            });
        }

        const { email, password, firstName, lastName, businessName, industry, phone } = req.body;

        // Check if user already exists
        const existingUser = await User.findOne({ email: email.toLowerCase() });
        if (existingUser) {
            return res.status(400).json({
                error: 'User with this email already exists',
            });
        }

        // Create user
        const user = new User({
            email: email.toLowerCase(),
            password,
            firstName,
            lastName,
            phone,
        });

        await user.save();

        // Create business if provided
        let business = null;
        if (businessName) {
            business = new Business({
                name: businessName,
                industry: industry || 'other',
                owner: user._id,
                contact: {
                    email: email.toLowerCase(),
                    phone: phone,
                    whatsappNumber: phone,
                },
            });

            await business.save();

            // Add business to user
            user.businesses.push({
                business: business._id,
                role: 'owner',
                permissions: [
                    'contacts.read', 'contacts.write', 'contacts.delete',
                    'campaigns.read', 'campaigns.write', 'campaigns.delete',
                    'analytics.read', 'settings.read', 'settings.write',
                    'billing.read', 'billing.write', 'users.read', 'users.write'
                ],
            });

            await user.save();
        }

        // Generate token
        const token = generateToken(user._id);

        // Log successful registration
        logger.info('User registered successfully', {
            userId: user._id,
            email: user.email,
            businessId: business?._id,
        });

        res.status(201).json({
            message: 'User registered successfully',
            token,
            user: {
                id: user._id,
                email: user.email,
                firstName: user.firstName,
                lastName: user.lastName,
                role: user.role,
                businesses: user.businesses,
            },
            business: business ? {
                id: business._id,
                name: business.name,
                industry: business.industry,
            } : null,
        });
    } catch (error) {
        logger.error('Registration error:', error);
        res.status(500).json({
            error: 'Registration failed',
        });
    }
};

// Login user
const login = async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                error: 'Validation failed',
                details: errors.array(),
            });
        }

        const { email, password } = req.body;
        const userAgent = req.get('User-Agent');
        const ip = req.ip;

        // Find user
        const user = await User.findOne({ email: email.toLowerCase() })
            .populate('businesses.business');

        if (!user) {
            logger.security.loginAttempt(email, false, ip, userAgent);
            return res.status(401).json({
                error: 'Invalid email or password',
            });
        }

        // Check password
        const isPasswordValid = await user.comparePassword(password);
        if (!isPasswordValid) {
            logger.security.loginAttempt(email, false, ip, userAgent);
            return res.status(401).json({
                error: 'Invalid email or password',
            });
        }

        // Check if account is active
        if (user.status !== 'active') {
            logger.security.loginAttempt(email, false, ip, userAgent);
            return res.status(401).json({
                error: 'Account is inactive',
            });
        }

        // Update last login
        user.lastLogin = new Date();
        await user.save();

        // Generate token
        const token = generateToken(user._id);

        // Log successful login
        logger.security.loginAttempt(email, true, ip, userAgent);

        res.json({
            message: 'Login successful',
            token,
            user: {
                id: user._id,
                email: user.email,
                firstName: user.firstName,
                lastName: user.lastName,
                fullName: user.fullName,
                role: user.role,
                avatar: user.avatar,
                language: user.language,
                timezone: user.timezone,
                lastLogin: user.lastLogin,
                businesses: user.businesses.map(b => ({
                    id: b.business._id,
                    name: b.business.name,
                    role: b.role,
                    permissions: b.permissions,
                })),
            },
        });
    } catch (error) {
        logger.error('Login error:', error);
        res.status(500).json({
            error: 'Login failed',
        });
    }
};

// Get current user profile
const getProfile = async (req, res) => {
    try {
        const user = await User.findById(req.user.id)
            .populate('businesses.business')
            .select('-password');

        res.json({
            user: {
                id: user._id,
                email: user.email,
                firstName: user.firstName,
                lastName: user.lastName,
                fullName: user.fullName,
                phone: user.phone,
                avatar: user.avatar,
                role: user.role,
                status: user.status,
                language: user.language,
                timezone: user.timezone,
                lastLogin: user.lastLogin,
                emailVerified: user.emailVerified,
                preferences: user.preferences,
                businesses: user.businesses.map(b => ({
                    id: b.business._id,
                    name: b.business.name,
                    industry: b.business.industry,
                    role: b.role,
                    permissions: b.permissions,
                    joinedAt: b.joinedAt,
                })),
            },
        });
    } catch (error) {
        logger.error('Get profile error:', error);
        res.status(500).json({
            error: 'Failed to get profile',
        });
    }
};

// Update user profile
const updateProfile = async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                error: 'Validation failed',
                details: errors.array(),
            });
        }

        const { firstName, lastName, phone, language, timezone, preferences } = req.body;

        const user = await User.findById(req.user.id);
        
        if (firstName) user.firstName = firstName;
        if (lastName) user.lastName = lastName;
        if (phone) user.phone = phone;
        if (language) user.language = language;
        if (timezone) user.timezone = timezone;
        if (preferences) user.preferences = { ...user.preferences, ...preferences };

        await user.save();

        logger.business.userAction(user._id, null, 'profile_updated', {
            updatedFields: Object.keys(req.body),
        });

        res.json({
            message: 'Profile updated successfully',
            user: {
                id: user._id,
                email: user.email,
                firstName: user.firstName,
                lastName: user.lastName,
                fullName: user.fullName,
                phone: user.phone,
                language: user.language,
                timezone: user.timezone,
                preferences: user.preferences,
            },
        });
    } catch (error) {
        logger.error('Update profile error:', error);
        res.status(500).json({
            error: 'Failed to update profile',
        });
    }
};

// Change password
const changePassword = async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                error: 'Validation failed',
                details: errors.array(),
            });
        }

        const { currentPassword, newPassword } = req.body;

        const user = await User.findById(req.user.id);

        // Verify current password
        const isCurrentPasswordValid = await user.comparePassword(currentPassword);
        if (!isCurrentPasswordValid) {
            return res.status(400).json({
                error: 'Current password is incorrect',
            });
        }

        // Update password
        user.password = newPassword;
        await user.save();

        logger.business.userAction(user._id, null, 'password_changed');

        res.json({
            message: 'Password changed successfully',
        });
    } catch (error) {
        logger.error('Change password error:', error);
        res.status(500).json({
            error: 'Failed to change password',
        });
    }
};

// Logout (client-side token invalidation)
const logout = async (req, res) => {
    try {
        logger.business.userAction(req.user.id, null, 'logout');
        
        res.json({
            message: 'Logged out successfully',
        });
    } catch (error) {
        logger.error('Logout error:', error);
        res.status(500).json({
            error: 'Logout failed',
        });
    }
};

module.exports = {
    register,
    login,
    getProfile,
    updateProfile,
    changePassword,
    logout,
};
