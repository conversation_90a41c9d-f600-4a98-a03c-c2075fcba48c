const { validationResult } = require('express-validator');
const { Automation, Contact, Business } = require('../models');
const logger = require('../services/logger');

// Get all automations for a business
const getAutomations = async (req, res) => {
    try {
        const businessId = req.business._id;
        const {
            page = 1,
            limit = 20,
            status,
            type,
            sortBy = 'createdAt',
            sortOrder = 'desc',
        } = req.query;

        // Build query
        const query = { business: businessId };
        if (status) query.status = status;
        if (type) query.type = type;

        // Pagination
        const skip = (parseInt(page) - 1) * parseInt(limit);
        const sortOptions = {};
        sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

        // Execute query
        const [automations, totalCount] = await Promise.all([
            Automation.find(query)
                .sort(sortOptions)
                .skip(skip)
                .limit(parseInt(limit))
                .populate('createdBy', 'firstName lastName')
                .select('-executions'), // Exclude executions for list view
            Automation.countDocuments(query),
        ]);

        const totalPages = Math.ceil(totalCount / parseInt(limit));

        res.json({
            automations,
            pagination: {
                currentPage: parseInt(page),
                totalPages,
                totalCount,
                hasNextPage: parseInt(page) < totalPages,
                hasPrevPage: parseInt(page) > 1,
                limit: parseInt(limit),
            },
        });
    } catch (error) {
        logger.business.apiError(req.business._id, 'GET /automations', error);
        res.status(500).json({
            error: 'Failed to fetch automations',
        });
    }
};

// Get single automation
const getAutomation = async (req, res) => {
    try {
        const { automationId } = req.params;
        const businessId = req.business._id;

        const automation = await Automation.findOne({
            _id: automationId,
            business: businessId,
        })
            .populate('createdBy', 'firstName lastName email')
            .populate('targeting.segments', 'name description')
            .populate('executions.contact', 'name phone');

        if (!automation) {
            return res.status(404).json({
                error: 'Automation not found',
            });
        }

        res.json({ automation });
    } catch (error) {
        logger.business.apiError(req.business._id, 'GET /automations/:id', error);
        res.status(500).json({
            error: 'Failed to fetch automation',
        });
    }
};

// Create new automation
const createAutomation = async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                error: 'Validation failed',
                details: errors.array(),
            });
        }

        const businessId = req.business._id;

        // Check if business has reached automation limit
        if (req.business.hasReachedLimit('automations')) {
            return res.status(403).json({
                error: 'Automation limit reached. Please upgrade your plan.',
            });
        }

        const automationData = {
            ...req.body,
            business: businessId,
            createdBy: req.user.id,
        };

        const automation = new Automation(automationData);
        await automation.save();

        // Increment business automation usage
        await req.business.incrementUsage('automations');

        logger.business.userAction(req.user.id, businessId, 'automation_created', {
            automationId: automation._id,
            automationName: automation.name,
            type: automation.type,
        });

        res.status(201).json({
            message: 'Automation created successfully',
            automation,
        });
    } catch (error) {
        logger.business.apiError(req.business._id, 'POST /automations', error);
        res.status(500).json({
            error: 'Failed to create automation',
        });
    }
};

// Update automation
const updateAutomation = async (req, res) => {
    try {
        const { automationId } = req.params;
        const businessId = req.business._id;
        const updateData = req.body;

        const automation = await Automation.findOne({
            _id: automationId,
            business: businessId,
        });

        if (!automation) {
            return res.status(404).json({
                error: 'Automation not found',
            });
        }

        // Update allowed fields
        const allowedFields = [
            'name', 'description', 'triggers', 'actions', 'targeting', 'settings'
        ];

        allowedFields.forEach(field => {
            if (updateData[field] !== undefined) {
                automation[field] = updateData[field];
            }
        });

        await automation.save();

        logger.business.userAction(req.user.id, businessId, 'automation_updated', {
            automationId,
            updatedFields: Object.keys(updateData),
        });

        res.json({
            message: 'Automation updated successfully',
            automation,
        });
    } catch (error) {
        logger.business.apiError(req.business._id, 'PUT /automations/:id', error);
        res.status(500).json({
            error: 'Failed to update automation',
        });
    }
};

// Delete automation
const deleteAutomation = async (req, res) => {
    try {
        const { automationId } = req.params;
        const businessId = req.business._id;

        const automation = await Automation.findOne({
            _id: automationId,
            business: businessId,
        });

        if (!automation) {
            return res.status(404).json({
                error: 'Automation not found',
            });
        }

        await Automation.findByIdAndDelete(automationId);

        // Decrement business automation usage
        req.business.usage.automations = Math.max(0, req.business.usage.automations - 1);
        await req.business.save();

        logger.business.userAction(req.user.id, businessId, 'automation_deleted', {
            automationId,
            automationName: automation.name,
        });

        res.json({
            message: 'Automation deleted successfully',
        });
    } catch (error) {
        logger.business.apiError(req.business._id, 'DELETE /automations/:id', error);
        res.status(500).json({
            error: 'Failed to delete automation',
        });
    }
};

// Toggle automation status
const toggleAutomationStatus = async (req, res) => {
    try {
        const { automationId } = req.params;
        const { status } = req.body;
        const businessId = req.business._id;

        if (!['active', 'inactive'].includes(status)) {
            return res.status(400).json({
                error: 'Status must be either "active" or "inactive"',
            });
        }

        const automation = await Automation.findOne({
            _id: automationId,
            business: businessId,
        });

        if (!automation) {
            return res.status(404).json({
                error: 'Automation not found',
            });
        }

        automation.status = status;
        await automation.save();

        logger.business.userAction(req.user.id, businessId, 'automation_status_changed', {
            automationId,
            newStatus: status,
        });

        res.json({
            message: `Automation ${status === 'active' ? 'activated' : 'deactivated'} successfully`,
            automation,
        });
    } catch (error) {
        logger.business.apiError(req.business._id, 'PUT /automations/:id/status', error);
        res.status(500).json({
            error: 'Failed to update automation status',
        });
    }
};

// Test automation with a sample contact
const testAutomation = async (req, res) => {
    try {
        const { automationId } = req.params;
        const { contactId, testMessage } = req.body;
        const businessId = req.business._id;

        const automation = await Automation.findOne({
            _id: automationId,
            business: businessId,
        });

        if (!automation) {
            return res.status(404).json({
                error: 'Automation not found',
            });
        }

        const contact = await Contact.findOne({
            _id: contactId,
            business: businessId,
        });

        if (!contact) {
            return res.status(404).json({
                error: 'Contact not found',
            });
        }

        // Create test message object
        const message = testMessage ? {
            content: { text: testMessage },
            type: 'text',
        } : null;

        // Check if automation should trigger
        const shouldTrigger = automation.shouldTrigger(contact, message, 'message_received');

        if (!shouldTrigger) {
            return res.json({
                triggered: false,
                reason: 'Automation conditions not met',
                automation: {
                    id: automation._id,
                    name: automation.name,
                    triggers: automation.triggers,
                },
            });
        }

        // For testing, we won't actually execute the automation
        // Just return what would happen
        res.json({
            triggered: true,
            automation: {
                id: automation._id,
                name: automation.name,
                actions: automation.actions,
            },
            contact: {
                id: contact._id,
                name: contact.name,
                phone: contact.phone,
            },
            testMessage,
        });
    } catch (error) {
        logger.business.apiError(req.business._id, 'POST /automations/:id/test', error);
        res.status(500).json({
            error: 'Failed to test automation',
        });
    }
};

// Get automation analytics
const getAutomationAnalytics = async (req, res) => {
    try {
        const { automationId } = req.params;
        const businessId = req.business._id;

        const automation = await Automation.findOne({
            _id: automationId,
            business: businessId,
        });

        if (!automation) {
            return res.status(404).json({
                error: 'Automation not found',
            });
        }

        // Get execution statistics
        const executionStats = await Automation.aggregate([
            { $match: { _id: automation._id } },
            { $unwind: '$executions' },
            {
                $group: {
                    _id: '$executions.status',
                    count: { $sum: 1 },
                }
            }
        ]);

        // Get recent executions
        const recentExecutions = automation.executions
            .sort((a, b) => b.triggeredAt - a.triggeredAt)
            .slice(0, 10);

        res.json({
            analytics: automation.analytics,
            successRate: automation.successRate,
            executionStats: executionStats.reduce((acc, stat) => {
                acc[stat._id] = stat.count;
                return acc;
            }, {}),
            recentExecutions,
        });
    } catch (error) {
        logger.business.apiError(req.business._id, 'GET /automations/:id/analytics', error);
        res.status(500).json({
            error: 'Failed to get automation analytics',
        });
    }
};

// Process incoming message for automations
const processMessageForAutomations = async (businessId, contact, message) => {
    try {
        // Get all active automations for the business
        const automations = await Automation.find({
            business: businessId,
            status: 'active',
            'triggers.keywords.0': { $exists: true }, // Has keyword triggers
        }).sort({ priority: -1 }); // Higher priority first

        for (const automation of automations) {
            try {
                if (automation.shouldTrigger(contact, message, 'message_received')) {
                    await automation.execute(contact, { message });
                    
                    logger.business.automationTriggered(
                        businessId,
                        automation._id,
                        contact._id,
                        'keyword_match'
                    );

                    // If automation has stopOnReply setting and this is a reply, stop processing
                    if (automation.settings.stopOnReply) {
                        break;
                    }
                }
            } catch (error) {
                logger.business.apiError(businessId, 'automation_execution', error, {
                    automationId: automation._id,
                    contactId: contact._id,
                });
            }
        }
    } catch (error) {
        logger.business.apiError(businessId, 'automation_processing', error);
    }
};

module.exports = {
    getAutomations,
    getAutomation,
    createAutomation,
    updateAutomation,
    deleteAutomation,
    toggleAutomationStatus,
    testAutomation,
    getAutomationAnalytics,
    processMessageForAutomations,
};
