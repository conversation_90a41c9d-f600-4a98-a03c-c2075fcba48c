const { validationResult } = require('express-validator');
const { Business, User, Contact, Conversation } = require('../models');
const logger = require('../services/logger');
const whatsappService = require('../services/whatsappService');

// Get business profile
const getBusinessProfile = async (req, res) => {
    try {
        const business = await Business.findById(req.business._id)
            .populate('owner', 'firstName lastName email');

        if (!business) {
            return res.status(404).json({
                error: 'Business not found',
            });
        }

        // Get team members count
        const teamMembersCount = await User.countDocuments({
            'businesses.business': business._id,
        });

        res.json({
            business: {
                ...business.toObject(),
                teamMembersCount,
            },
        });
    } catch (error) {
        logger.business.apiError(req.business._id, 'GET /business/profile', error);
        res.status(500).json({
            error: 'Failed to fetch business profile',
        });
    }
};

// Update business profile
const updateBusinessProfile = async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                error: 'Validation failed',
                details: errors.array(),
            });
        }

        const businessId = req.business._id;
        const updateData = req.body;

        // Check if user has permission to update business settings
        if (!req.user.hasPermission(businessId, 'settings.write')) {
            return res.status(403).json({
                error: 'Insufficient permissions to update business profile',
            });
        }

        const business = await Business.findById(businessId);

        // Update allowed fields
        const allowedFields = [
            'name', 'description', 'industry', 'logo',
            'contact', 'address', 'settings'
        ];

        allowedFields.forEach(field => {
            if (updateData[field] !== undefined) {
                if (field === 'contact' || field === 'address' || field === 'settings') {
                    business[field] = { ...business[field], ...updateData[field] };
                } else {
                    business[field] = updateData[field];
                }
            }
        });

        await business.save();

        logger.business.userAction(req.user.id, businessId, 'business_profile_updated', {
            updatedFields: Object.keys(updateData),
        });

        res.json({
            message: 'Business profile updated successfully',
            business,
        });
    } catch (error) {
        logger.business.apiError(req.business._id, 'PUT /business/profile', error);
        res.status(500).json({
            error: 'Failed to update business profile',
        });
    }
};

// Get WhatsApp configuration
const getWhatsAppConfig = async (req, res) => {
    try {
        const business = await Business.findById(req.business._id)
            .select('whatsappConfig');

        // Hide sensitive information
        const config = {
            phoneNumberId: business.whatsappConfig.phoneNumberId,
            businessAccountId: business.whatsappConfig.businessAccountId,
            isConnected: business.whatsappConfig.isConnected,
            lastConnected: business.whatsappConfig.lastConnected,
        };

        res.json({ whatsappConfig: config });
    } catch (error) {
        logger.business.apiError(req.business._id, 'GET /business/whatsapp-config', error);
        res.status(500).json({
            error: 'Failed to fetch WhatsApp configuration',
        });
    }
};

// Update WhatsApp configuration
const updateWhatsAppConfig = async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                error: 'Validation failed',
                details: errors.array(),
            });
        }

        const businessId = req.business._id;
        const { phoneNumberId, businessAccountId, accessToken, webhookVerifyToken } = req.body;

        // Check permissions
        if (!req.user.hasPermission(businessId, 'settings.write')) {
            return res.status(403).json({
                error: 'Insufficient permissions to update WhatsApp configuration',
            });
        }

        const business = await Business.findById(businessId);

        // Update WhatsApp configuration
        business.whatsappConfig = {
            ...business.whatsappConfig,
            phoneNumberId,
            businessAccountId,
            accessToken,
            webhookVerifyToken,
            isConnected: false, // Will be verified in next step
        };

        await business.save();

        // Test the connection
        try {
            // You could add a test API call here to verify the configuration
            business.whatsappConfig.isConnected = true;
            business.whatsappConfig.lastConnected = new Date();
            await business.save();
        } catch (testError) {
            logger.business.whatsappError(businessId, testError, {
                action: 'config_test',
            });
        }

        logger.business.userAction(req.user.id, businessId, 'whatsapp_config_updated');

        res.json({
            message: 'WhatsApp configuration updated successfully',
            isConnected: business.whatsappConfig.isConnected,
        });
    } catch (error) {
        logger.business.apiError(req.business._id, 'PUT /business/whatsapp-config', error);
        res.status(500).json({
            error: 'Failed to update WhatsApp configuration',
        });
    }
};

// Get team members
const getTeamMembers = async (req, res) => {
    try {
        const businessId = req.business._id;

        const teamMembers = await User.find({
            'businesses.business': businessId,
        })
            .select('firstName lastName email avatar status lastLogin businesses.$')
            .lean();

        // Format the response to include business-specific role and permissions
        const formattedMembers = teamMembers.map(member => {
            const businessMembership = member.businesses[0];
            return {
                id: member._id,
                firstName: member.firstName,
                lastName: member.lastName,
                email: member.email,
                avatar: member.avatar,
                status: member.status,
                lastLogin: member.lastLogin,
                role: businessMembership.role,
                permissions: businessMembership.permissions,
                joinedAt: businessMembership.joinedAt,
            };
        });

        res.json({
            teamMembers: formattedMembers,
            count: formattedMembers.length,
        });
    } catch (error) {
        logger.business.apiError(req.business._id, 'GET /business/team', error);
        res.status(500).json({
            error: 'Failed to fetch team members',
        });
    }
};

// Invite team member
const inviteTeamMember = async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                error: 'Validation failed',
                details: errors.array(),
            });
        }

        const businessId = req.business._id;
        const { email, role, permissions } = req.body;

        // Check permissions
        if (!req.user.hasPermission(businessId, 'users.write')) {
            return res.status(403).json({
                error: 'Insufficient permissions to invite team members',
            });
        }

        // Check if business has reached user limit
        if (req.business.hasReachedLimit('users')) {
            return res.status(403).json({
                error: 'User limit reached. Please upgrade your plan.',
            });
        }

        // Check if user already exists
        let user = await User.findOne({ email: email.toLowerCase() });

        if (user) {
            // Check if user is already a member of this business
            const existingMembership = user.businesses.find(
                b => b.business.toString() === businessId.toString()
            );

            if (existingMembership) {
                return res.status(400).json({
                    error: 'User is already a member of this business',
                });
            }

            // Add business to existing user
            user.businesses.push({
                business: businessId,
                role,
                permissions: permissions || [],
            });

            await user.save();
        } else {
            // Create new user with invitation
            user = new User({
                email: email.toLowerCase(),
                firstName: 'Invited',
                lastName: 'User',
                password: 'temporary_password', // Will be changed on first login
                status: 'inactive', // Will be activated when user accepts invitation
                businesses: [{
                    business: businessId,
                    role,
                    permissions: permissions || [],
                }],
            });

            await user.save();
        }

        // Increment business user usage
        await req.business.incrementUsage('users');

        // TODO: Send invitation email

        logger.business.userAction(req.user.id, businessId, 'team_member_invited', {
            invitedEmail: email,
            role,
        });

        res.status(201).json({
            message: 'Team member invited successfully',
            user: {
                id: user._id,
                email: user.email,
                role,
                permissions,
                status: user.status,
            },
        });
    } catch (error) {
        logger.business.apiError(req.business._id, 'POST /business/team/invite', error);
        res.status(500).json({
            error: 'Failed to invite team member',
        });
    }
};

// Update team member role/permissions
const updateTeamMember = async (req, res) => {
    try {
        const { userId } = req.params;
        const { role, permissions } = req.body;
        const businessId = req.business._id;

        // Check permissions
        if (!req.user.hasPermission(businessId, 'users.write')) {
            return res.status(403).json({
                error: 'Insufficient permissions to update team members',
            });
        }

        const user = await User.findById(userId);
        if (!user) {
            return res.status(404).json({
                error: 'User not found',
            });
        }

        // Find business membership
        const businessMembership = user.businesses.find(
            b => b.business.toString() === businessId.toString()
        );

        if (!businessMembership) {
            return res.status(404).json({
                error: 'User is not a member of this business',
            });
        }

        // Cannot modify owner role
        if (businessMembership.role === 'owner') {
            return res.status(400).json({
                error: 'Cannot modify owner role',
            });
        }

        // Update role and permissions
        if (role) businessMembership.role = role;
        if (permissions) businessMembership.permissions = permissions;

        await user.save();

        logger.business.userAction(req.user.id, businessId, 'team_member_updated', {
            updatedUserId: userId,
            newRole: role,
        });

        res.json({
            message: 'Team member updated successfully',
            user: {
                id: user._id,
                role: businessMembership.role,
                permissions: businessMembership.permissions,
            },
        });
    } catch (error) {
        logger.business.apiError(req.business._id, 'PUT /business/team/:userId', error);
        res.status(500).json({
            error: 'Failed to update team member',
        });
    }
};

// Remove team member
const removeTeamMember = async (req, res) => {
    try {
        const { userId } = req.params;
        const businessId = req.business._id;

        // Check permissions
        if (!req.user.hasPermission(businessId, 'users.write')) {
            return res.status(403).json({
                error: 'Insufficient permissions to remove team members',
            });
        }

        const user = await User.findById(userId);
        if (!user) {
            return res.status(404).json({
                error: 'User not found',
            });
        }

        // Find business membership
        const membershipIndex = user.businesses.findIndex(
            b => b.business.toString() === businessId.toString()
        );

        if (membershipIndex === -1) {
            return res.status(404).json({
                error: 'User is not a member of this business',
            });
        }

        // Cannot remove owner
        if (user.businesses[membershipIndex].role === 'owner') {
            return res.status(400).json({
                error: 'Cannot remove business owner',
            });
        }

        // Remove business membership
        user.businesses.splice(membershipIndex, 1);
        await user.save();

        // Decrement business user usage
        req.business.usage.users = Math.max(1, req.business.usage.users - 1);
        await req.business.save();

        logger.business.userAction(req.user.id, businessId, 'team_member_removed', {
            removedUserId: userId,
        });

        res.json({
            message: 'Team member removed successfully',
        });
    } catch (error) {
        logger.business.apiError(req.business._id, 'DELETE /business/team/:userId', error);
        res.status(500).json({
            error: 'Failed to remove team member',
        });
    }
};

module.exports = {
    getBusinessProfile,
    updateBusinessProfile,
    getWhatsAppConfig,
    updateWhatsAppConfig,
    getTeamMembers,
    inviteTeamMember,
    updateTeamMember,
    removeTeamMember,
};
