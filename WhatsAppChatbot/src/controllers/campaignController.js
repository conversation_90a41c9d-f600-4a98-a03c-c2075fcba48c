const { validationResult } = require('express-validator');
const { Campaign, Contact, Business } = require('../models');
const whatsappService = require('../services/whatsappService');
const logger = require('../services/logger');

// Get all campaigns for a business
const getCampaigns = async (req, res) => {
    try {
        const businessId = req.business._id;
        const {
            page = 1,
            limit = 20,
            status,
            type,
            sortBy = 'createdAt',
            sortOrder = 'desc',
        } = req.query;

        // Build query
        const query = { business: businessId };
        if (status) query.status = status;
        if (type) query.type = type;

        // Pagination
        const skip = (parseInt(page) - 1) * parseInt(limit);
        const sortOptions = {};
        sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

        // Execute query
        const [campaigns, totalCount] = await Promise.all([
            Campaign.find(query)
                .sort(sortOptions)
                .skip(skip)
                .limit(parseInt(limit))
                .populate('createdBy', 'firstName lastName')
                .select('-message.content'), // Exclude message content for list view
            Campaign.countDocuments(query),
        ]);

        const totalPages = Math.ceil(totalCount / parseInt(limit));

        res.json({
            campaigns,
            pagination: {
                currentPage: parseInt(page),
                totalPages,
                totalCount,
                hasNextPage: parseInt(page) < totalPages,
                hasPrevPage: parseInt(page) > 1,
                limit: parseInt(limit),
            },
        });
    } catch (error) {
        logger.business.apiError(req.business._id, 'GET /campaigns', error);
        res.status(500).json({
            error: 'Failed to fetch campaigns',
        });
    }
};

// Get single campaign
const getCampaign = async (req, res) => {
    try {
        const { campaignId } = req.params;
        const businessId = req.business._id;

        const campaign = await Campaign.findOne({
            _id: campaignId,
            business: businessId,
        })
            .populate('createdBy', 'firstName lastName email')
            .populate('targeting.segments', 'name description');

        if (!campaign) {
            return res.status(404).json({
                error: 'Campaign not found',
            });
        }

        res.json({ campaign });
    } catch (error) {
        logger.business.apiError(req.business._id, 'GET /campaigns/:id', error);
        res.status(500).json({
            error: 'Failed to fetch campaign',
        });
    }
};

// Create new campaign
const createCampaign = async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                error: 'Validation failed',
                details: errors.array(),
            });
        }

        const businessId = req.business._id;

        // Check if business has reached campaign limit
        if (req.business.hasReachedLimit('campaigns')) {
            return res.status(403).json({
                error: 'Campaign limit reached. Please upgrade your plan.',
            });
        }

        const campaignData = {
            ...req.body,
            business: businessId,
            createdBy: req.user.id,
        };

        const campaign = new Campaign(campaignData);
        await campaign.save();

        // Increment business campaign usage
        await req.business.incrementUsage('campaigns');

        logger.business.userAction(req.user.id, businessId, 'campaign_created', {
            campaignId: campaign._id,
            campaignName: campaign.name,
            type: campaign.type,
        });

        res.status(201).json({
            message: 'Campaign created successfully',
            campaign,
        });
    } catch (error) {
        logger.business.apiError(req.business._id, 'POST /campaigns', error);
        res.status(500).json({
            error: 'Failed to create campaign',
        });
    }
};

// Update campaign
const updateCampaign = async (req, res) => {
    try {
        const { campaignId } = req.params;
        const businessId = req.business._id;
        const updateData = req.body;

        const campaign = await Campaign.findOne({
            _id: campaignId,
            business: businessId,
        });

        if (!campaign) {
            return res.status(404).json({
                error: 'Campaign not found',
            });
        }

        // Cannot update running or completed campaigns
        if (['running', 'completed'].includes(campaign.status)) {
            return res.status(400).json({
                error: 'Cannot update running or completed campaigns',
            });
        }

        // Update allowed fields
        const allowedFields = [
            'name', 'description', 'message', 'targeting', 'scheduling'
        ];

        allowedFields.forEach(field => {
            if (updateData[field] !== undefined) {
                campaign[field] = updateData[field];
            }
        });

        await campaign.save();

        logger.business.userAction(req.user.id, businessId, 'campaign_updated', {
            campaignId,
            updatedFields: Object.keys(updateData),
        });

        res.json({
            message: 'Campaign updated successfully',
            campaign,
        });
    } catch (error) {
        logger.business.apiError(req.business._id, 'PUT /campaigns/:id', error);
        res.status(500).json({
            error: 'Failed to update campaign',
        });
    }
};

// Delete campaign
const deleteCampaign = async (req, res) => {
    try {
        const { campaignId } = req.params;
        const businessId = req.business._id;

        const campaign = await Campaign.findOne({
            _id: campaignId,
            business: businessId,
        });

        if (!campaign) {
            return res.status(404).json({
                error: 'Campaign not found',
            });
        }

        // Cannot delete running campaigns
        if (campaign.status === 'running') {
            return res.status(400).json({
                error: 'Cannot delete running campaign. Pause it first.',
            });
        }

        await Campaign.findByIdAndDelete(campaignId);

        // Decrement business campaign usage
        req.business.usage.campaigns = Math.max(0, req.business.usage.campaigns - 1);
        await req.business.save();

        logger.business.userAction(req.user.id, businessId, 'campaign_deleted', {
            campaignId,
            campaignName: campaign.name,
        });

        res.json({
            message: 'Campaign deleted successfully',
        });
    } catch (error) {
        logger.business.apiError(req.business._id, 'DELETE /campaigns/:id', error);
        res.status(500).json({
            error: 'Failed to delete campaign',
        });
    }
};

// Start campaign
const startCampaign = async (req, res) => {
    try {
        const { campaignId } = req.params;
        const businessId = req.business._id;

        const campaign = await Campaign.findOne({
            _id: campaignId,
            business: businessId,
        });

        if (!campaign) {
            return res.status(404).json({
                error: 'Campaign not found',
            });
        }

        if (campaign.status !== 'draft' && campaign.status !== 'paused') {
            return res.status(400).json({
                error: 'Campaign can only be started from draft or paused status',
            });
        }

        // Get target contacts
        const targetContacts = await getTargetContacts(campaign, businessId);

        if (targetContacts.length === 0) {
            return res.status(400).json({
                error: 'No target contacts found for this campaign',
            });
        }

        // Check message limits
        const business = await Business.findById(businessId);
        const remainingMessages = business.limits.monthlyMessages - business.usage.monthlyMessages;

        if (targetContacts.length > remainingMessages) {
            return res.status(403).json({
                error: `Not enough message credits. Need ${targetContacts.length}, have ${remainingMessages}`,
            });
        }

        // Update campaign analytics
        campaign.analytics.totalRecipients = targetContacts.length;
        await campaign.start();

        // Start sending messages (this would typically be done in a background job)
        if (campaign.scheduling.type === 'immediate') {
            await processCampaignMessages(campaign, targetContacts, business);
        }

        logger.business.campaignStarted(businessId, campaignId, targetContacts.length);

        res.json({
            message: 'Campaign started successfully',
            campaign,
            targetContacts: targetContacts.length,
        });
    } catch (error) {
        logger.business.apiError(req.business._id, 'POST /campaigns/:id/start', error);
        res.status(500).json({
            error: 'Failed to start campaign',
        });
    }
};

// Pause campaign
const pauseCampaign = async (req, res) => {
    try {
        const { campaignId } = req.params;
        const businessId = req.business._id;

        const campaign = await Campaign.findOne({
            _id: campaignId,
            business: businessId,
        });

        if (!campaign) {
            return res.status(404).json({
                error: 'Campaign not found',
            });
        }

        if (campaign.status !== 'running') {
            return res.status(400).json({
                error: 'Only running campaigns can be paused',
            });
        }

        await campaign.pause();

        logger.business.userAction(req.user.id, businessId, 'campaign_paused', {
            campaignId,
        });

        res.json({
            message: 'Campaign paused successfully',
            campaign,
        });
    } catch (error) {
        logger.business.apiError(req.business._id, 'POST /campaigns/:id/pause', error);
        res.status(500).json({
            error: 'Failed to pause campaign',
        });
    }
};

// Get campaign analytics
const getCampaignAnalytics = async (req, res) => {
    try {
        const { campaignId } = req.params;
        const businessId = req.business._id;

        const campaign = await Campaign.findOne({
            _id: campaignId,
            business: businessId,
        });

        if (!campaign) {
            return res.status(404).json({
                error: 'Campaign not found',
            });
        }

        // Calculate analytics
        await campaign.calculateAnalytics();

        res.json({
            analytics: campaign.analytics,
            engagementRate: campaign.engagementRate,
            execution: campaign.execution,
        });
    } catch (error) {
        logger.business.apiError(req.business._id, 'GET /campaigns/:id/analytics', error);
        res.status(500).json({
            error: 'Failed to get campaign analytics',
        });
    }
};

// Helper function to get target contacts based on campaign targeting
async function getTargetContacts(campaign, businessId) {
    const query = {
        business: businessId,
        status: 'active', // Only send to active contacts
    };

    // Apply targeting filters
    if (campaign.targeting.tags && campaign.targeting.tags.length > 0) {
        query.tags = { $in: campaign.targeting.tags };
    }

    if (campaign.targeting.segments && campaign.targeting.segments.length > 0) {
        query.segments = { $in: campaign.targeting.segments };
    }

    // Apply custom filters
    if (campaign.targeting.customFilters && campaign.targeting.customFilters.length > 0) {
        campaign.targeting.customFilters.forEach(filter => {
            switch (filter.operator) {
                case 'equals':
                    query[filter.field] = filter.value;
                    break;
                case 'not_equals':
                    query[filter.field] = { $ne: filter.value };
                    break;
                case 'contains':
                    query[filter.field] = { $regex: filter.value, $options: 'i' };
                    break;
                case 'greater_than':
                    query[filter.field] = { $gt: filter.value };
                    break;
                case 'less_than':
                    query[filter.field] = { $lt: filter.value };
                    break;
            }
        });
    }

    // Exclude specific contacts
    if (campaign.targeting.excludeContacts && campaign.targeting.excludeContacts.length > 0) {
        query._id = { $nin: campaign.targeting.excludeContacts };
    }

    return await Contact.find(query).select('_id whatsappId name phone');
}

// Helper function to process campaign messages
async function processCampaignMessages(campaign, contacts, business) {
    let sentCount = 0;
    let failedCount = 0;

    for (const contact of contacts) {
        try {
            let messageResult;

            switch (campaign.message.type) {
                case 'text':
                    messageResult = await whatsappService.sendTextMessage(
                        business._id,
                        contact.whatsappId,
                        campaign.message.content.text,
                        business.whatsappConfig.accessToken,
                        business.whatsappConfig.phoneNumberId
                    );
                    break;

                case 'template':
                    messageResult = await whatsappService.sendTemplateMessage(
                        business._id,
                        contact.whatsappId,
                        campaign.message.content.template.name,
                        campaign.message.content.template.language,
                        campaign.message.content.template.parameters,
                        business.whatsappConfig.accessToken,
                        business.whatsappConfig.phoneNumberId
                    );
                    break;

                default:
                    throw new Error(`Unsupported message type: ${campaign.message.type}`);
            }

            sentCount++;
            await business.incrementUsage('monthlyMessages');

        } catch (error) {
            failedCount++;
            campaign.execution.errors.push({
                message: error.message,
                contactId: contact._id,
            });
        }

        // Add small delay to respect rate limits
        await new Promise(resolve => setTimeout(resolve, 100));
    }

    // Update campaign analytics
    campaign.analytics.sent = sentCount;
    campaign.analytics.failed = failedCount;
    campaign.execution.lastRunAt = new Date();

    if (sentCount + failedCount >= contacts.length) {
        await campaign.complete();
    }

    await campaign.save();
}

module.exports = {
    getCampaigns,
    getCampaign,
    createCampaign,
    updateCampaign,
    deleteCampaign,
    startCampaign,
    pauseCampaign,
    getCampaignAnalytics,
};
