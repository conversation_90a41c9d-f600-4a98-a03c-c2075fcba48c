const { validationResult } = require('express-validator');
const { Contact, Business, Conversation } = require('../models');
const logger = require('../services/logger');

// Get all contacts for a business
const getContacts = async (req, res) => {
    try {
        const businessId = req.business._id;
        const {
            page = 1,
            limit = 20,
            search,
            tags,
            status,
            segment,
            sortBy = 'createdAt',
            sortOrder = 'desc',
        } = req.query;

        // Build query
        const query = { business: businessId };

        // Search filter
        if (search) {
            query.$or = [
                { name: { $regex: search, $options: 'i' } },
                { phone: { $regex: search, $options: 'i' } },
                { email: { $regex: search, $options: 'i' } },
            ];
        }

        // Tags filter
        if (tags) {
            const tagArray = tags.split(',');
            query.tags = { $in: tagArray };
        }

        // Status filter
        if (status) {
            query.status = status;
        }

        // Segment filter
        if (segment) {
            query.segments = segment;
        }

        // Pagination
        const skip = (parseInt(page) - 1) * parseInt(limit);
        const sortOptions = {};
        sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

        // Execute query
        const [contacts, totalCount] = await Promise.all([
            Contact.find(query)
                .sort(sortOptions)
                .skip(skip)
                .limit(parseInt(limit))
                .populate('segments', 'name')
                .populate('assignedTo', 'firstName lastName email'),
            Contact.countDocuments(query),
        ]);

        // Calculate pagination info
        const totalPages = Math.ceil(totalCount / parseInt(limit));
        const hasNextPage = parseInt(page) < totalPages;
        const hasPrevPage = parseInt(page) > 1;

        res.json({
            contacts,
            pagination: {
                currentPage: parseInt(page),
                totalPages,
                totalCount,
                hasNextPage,
                hasPrevPage,
                limit: parseInt(limit),
            },
        });
    } catch (error) {
        logger.business.apiError(req.business._id, 'GET /contacts', error);
        res.status(500).json({
            error: 'Failed to fetch contacts',
        });
    }
};

// Get single contact
const getContact = async (req, res) => {
    try {
        const { contactId } = req.params;
        const businessId = req.business._id;

        const contact = await Contact.findOne({
            _id: contactId,
            business: businessId,
        })
            .populate('segments', 'name description')
            .populate('assignedTo', 'firstName lastName email avatar');

        if (!contact) {
            return res.status(404).json({
                error: 'Contact not found',
            });
        }

        // Get conversation count and last conversation
        const [conversationCount, lastConversation] = await Promise.all([
            Conversation.countDocuments({
                business: businessId,
                contact: contactId,
            }),
            Conversation.findOne({
                business: businessId,
                contact: contactId,
            })
                .sort({ updatedAt: -1 })
                .select('status lastMessage updatedAt'),
        ]);

        res.json({
            contact: {
                ...contact.toObject(),
                conversationCount,
                lastConversation,
            },
        });
    } catch (error) {
        logger.business.apiError(req.business._id, 'GET /contacts/:id', error);
        res.status(500).json({
            error: 'Failed to fetch contact',
        });
    }
};

// Create new contact
const createContact = async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                error: 'Validation failed',
                details: errors.array(),
            });
        }

        const businessId = req.business._id;
        const { name, phone, email, tags, customFields, assignedTo } = req.body;

        // Check if business has reached contact limit
        if (req.business.hasReachedLimit('contacts')) {
            return res.status(403).json({
                error: 'Contact limit reached. Please upgrade your plan.',
            });
        }

        // Check if contact already exists
        const existingContact = await Contact.findOne({
            business: businessId,
            $or: [
                { phone },
                ...(email ? [{ email }] : []),
            ],
        });

        if (existingContact) {
            return res.status(400).json({
                error: 'Contact with this phone number or email already exists',
            });
        }

        // Create contact
        const contact = new Contact({
            business: businessId,
            whatsappId: phone, // Will be updated when first message is received
            name,
            phone,
            email,
            tags: tags || [],
            customFields: customFields || [],
            source: 'manual',
            assignedTo: assignedTo || null,
        });

        await contact.save();

        // Increment business contact usage
        await req.business.incrementUsage('contacts');

        // Calculate initial engagement score
        contact.calculateEngagementScore();
        await contact.save();

        logger.business.userAction(req.user.id, businessId, 'contact_created', {
            contactId: contact._id,
            contactName: name,
        });

        res.status(201).json({
            message: 'Contact created successfully',
            contact,
        });
    } catch (error) {
        logger.business.apiError(req.business._id, 'POST /contacts', error);
        res.status(500).json({
            error: 'Failed to create contact',
        });
    }
};

// Update contact
const updateContact = async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                error: 'Validation failed',
                details: errors.array(),
            });
        }

        const { contactId } = req.params;
        const businessId = req.business._id;
        const updateData = req.body;

        const contact = await Contact.findOne({
            _id: contactId,
            business: businessId,
        });

        if (!contact) {
            return res.status(404).json({
                error: 'Contact not found',
            });
        }

        // Update allowed fields
        const allowedFields = [
            'name', 'email', 'tags', 'customFields', 'status',
            'language', 'assignedTo', 'preferences'
        ];

        allowedFields.forEach(field => {
            if (updateData[field] !== undefined) {
                contact[field] = updateData[field];
            }
        });

        // Recalculate engagement score if relevant fields changed
        if (updateData.tags || updateData.status) {
            contact.calculateEngagementScore();
        }

        await contact.save();

        logger.business.userAction(req.user.id, businessId, 'contact_updated', {
            contactId: contact._id,
            updatedFields: Object.keys(updateData),
        });

        res.json({
            message: 'Contact updated successfully',
            contact,
        });
    } catch (error) {
        logger.business.apiError(req.business._id, 'PUT /contacts/:id', error);
        res.status(500).json({
            error: 'Failed to update contact',
        });
    }
};

// Delete contact
const deleteContact = async (req, res) => {
    try {
        const { contactId } = req.params;
        const businessId = req.business._id;

        const contact = await Contact.findOne({
            _id: contactId,
            business: businessId,
        });

        if (!contact) {
            return res.status(404).json({
                error: 'Contact not found',
            });
        }

        // Check if contact has active conversations
        const activeConversations = await Conversation.countDocuments({
            business: businessId,
            contact: contactId,
            status: { $in: ['open', 'pending'] },
        });

        if (activeConversations > 0) {
            return res.status(400).json({
                error: 'Cannot delete contact with active conversations',
            });
        }

        await Contact.findByIdAndDelete(contactId);

        // Decrement business contact usage
        req.business.usage.contacts = Math.max(0, req.business.usage.contacts - 1);
        await req.business.save();

        logger.business.userAction(req.user.id, businessId, 'contact_deleted', {
            contactId,
            contactName: contact.name,
        });

        res.json({
            message: 'Contact deleted successfully',
        });
    } catch (error) {
        logger.business.apiError(req.business._id, 'DELETE /contacts/:id', error);
        res.status(500).json({
            error: 'Failed to delete contact',
        });
    }
};

// Add note to contact
const addNote = async (req, res) => {
    try {
        const { contactId } = req.params;
        const { content } = req.body;
        const businessId = req.business._id;

        if (!content || content.trim().length === 0) {
            return res.status(400).json({
                error: 'Note content is required',
            });
        }

        const contact = await Contact.findOne({
            _id: contactId,
            business: businessId,
        });

        if (!contact) {
            return res.status(404).json({
                error: 'Contact not found',
            });
        }

        await contact.addNote(content.trim(), req.user.id);

        logger.business.userAction(req.user.id, businessId, 'contact_note_added', {
            contactId,
        });

        res.json({
            message: 'Note added successfully',
            contact,
        });
    } catch (error) {
        logger.business.apiError(req.business._id, 'POST /contacts/:id/notes', error);
        res.status(500).json({
            error: 'Failed to add note',
        });
    }
};

// Bulk operations
const bulkUpdateContacts = async (req, res) => {
    try {
        const { contactIds, updates } = req.body;
        const businessId = req.business._id;

        if (!contactIds || !Array.isArray(contactIds) || contactIds.length === 0) {
            return res.status(400).json({
                error: 'Contact IDs array is required',
            });
        }

        if (!updates || Object.keys(updates).length === 0) {
            return res.status(400).json({
                error: 'Updates object is required',
            });
        }

        // Validate that all contacts belong to the business
        const contacts = await Contact.find({
            _id: { $in: contactIds },
            business: businessId,
        });

        if (contacts.length !== contactIds.length) {
            return res.status(400).json({
                error: 'Some contacts not found or do not belong to this business',
            });
        }

        // Perform bulk update
        const allowedFields = ['tags', 'status', 'assignedTo', 'language'];
        const updateQuery = {};

        allowedFields.forEach(field => {
            if (updates[field] !== undefined) {
                if (field === 'tags' && updates.addTags) {
                    updateQuery.$addToSet = { tags: { $each: updates.addTags } };
                } else if (field === 'tags' && updates.removeTags) {
                    updateQuery.$pullAll = { tags: updates.removeTags };
                } else {
                    updateQuery[field] = updates[field];
                }
            }
        });

        const result = await Contact.updateMany(
            { _id: { $in: contactIds }, business: businessId },
            updateQuery
        );

        logger.business.userAction(req.user.id, businessId, 'contacts_bulk_updated', {
            contactCount: contactIds.length,
            updates: Object.keys(updates),
        });

        res.json({
            message: 'Contacts updated successfully',
            modifiedCount: result.modifiedCount,
        });
    } catch (error) {
        logger.business.apiError(req.business._id, 'PUT /contacts/bulk', error);
        res.status(500).json({
            error: 'Failed to update contacts',
        });
    }
};

// Import contacts from CSV
const importContacts = async (req, res) => {
    try {
        const { contacts } = req.body;
        const businessId = req.business._id;

        if (!contacts || !Array.isArray(contacts) || contacts.length === 0) {
            return res.status(400).json({
                error: 'Contacts array is required',
            });
        }

        // Check if business has enough contact limit
        const currentContactCount = req.business.usage.contacts;
        const availableSlots = req.business.limits.contacts - currentContactCount;

        if (contacts.length > availableSlots) {
            return res.status(403).json({
                error: `Cannot import ${contacts.length} contacts. Only ${availableSlots} slots available.`,
            });
        }

        const results = {
            imported: 0,
            skipped: 0,
            errors: [],
        };

        for (const contactData of contacts) {
            try {
                // Validate required fields
                if (!contactData.phone) {
                    results.errors.push({
                        data: contactData,
                        error: 'Phone number is required',
                    });
                    results.skipped++;
                    continue;
                }

                // Check if contact already exists
                const existingContact = await Contact.findOne({
                    business: businessId,
                    phone: contactData.phone,
                });

                if (existingContact) {
                    results.skipped++;
                    continue;
                }

                // Create contact
                const contact = new Contact({
                    business: businessId,
                    whatsappId: contactData.phone,
                    name: contactData.name || '',
                    phone: contactData.phone,
                    email: contactData.email || '',
                    tags: contactData.tags || [],
                    source: 'import',
                });

                await contact.save();
                results.imported++;

            } catch (error) {
                results.errors.push({
                    data: contactData,
                    error: error.message,
                });
                results.skipped++;
            }
        }

        // Update business usage
        await req.business.incrementUsage('contacts', results.imported);

        logger.business.userAction(req.user.id, businessId, 'contacts_imported', {
            imported: results.imported,
            skipped: results.skipped,
            errors: results.errors.length,
        });

        res.json({
            message: 'Import completed',
            results,
        });
    } catch (error) {
        logger.business.apiError(req.business._id, 'POST /contacts/import', error);
        res.status(500).json({
            error: 'Failed to import contacts',
        });
    }
};

// Export contacts to CSV
const exportContacts = async (req, res) => {
    try {
        const businessId = req.business._id;
        const { format = 'json', filters = {} } = req.query;

        // Build query with filters
        const query = { business: businessId, ...filters };

        const contacts = await Contact.find(query)
            .select('name phone email tags status createdAt lastMessageAt totalMessages')
            .lean();

        if (format === 'csv') {
            // Convert to CSV format
            const csvHeader = 'Name,Phone,Email,Tags,Status,Created At,Last Message,Total Messages\n';
            const csvRows = contacts.map(contact => {
                return [
                    contact.name || '',
                    contact.phone || '',
                    contact.email || '',
                    (contact.tags || []).join(';'),
                    contact.status || '',
                    contact.createdAt ? new Date(contact.createdAt).toISOString() : '',
                    contact.lastMessageAt ? new Date(contact.lastMessageAt).toISOString() : '',
                    contact.totalMessages || 0,
                ].map(field => `"${field}"`).join(',');
            }).join('\n');

            res.setHeader('Content-Type', 'text/csv');
            res.setHeader('Content-Disposition', 'attachment; filename=contacts.csv');
            res.send(csvHeader + csvRows);
        } else {
            res.json({
                contacts,
                count: contacts.length,
                exportedAt: new Date().toISOString(),
            });
        }

        logger.business.userAction(req.user.id, businessId, 'contacts_exported', {
            count: contacts.length,
            format,
        });
    } catch (error) {
        logger.business.apiError(req.business._id, 'GET /contacts/export', error);
        res.status(500).json({
            error: 'Failed to export contacts',
        });
    }
};

// Get contact analytics
const getContactAnalytics = async (req, res) => {
    try {
        const businessId = req.business._id;

        const analytics = await Contact.aggregate([
            { $match: { business: businessId } },
            {
                $group: {
                    _id: null,
                    totalContacts: { $sum: 1 },
                    activeContacts: {
                        $sum: {
                            $cond: [{ $eq: ['$status', 'active'] }, 1, 0]
                        }
                    },
                    blockedContacts: {
                        $sum: {
                            $cond: [{ $eq: ['$status', 'blocked'] }, 1, 0]
                        }
                    },
                    unsubscribedContacts: {
                        $sum: {
                            $cond: [{ $eq: ['$status', 'unsubscribed'] }, 1, 0]
                        }
                    },
                    averageEngagementScore: { $avg: '$engagement.score' },
                    totalMessages: { $sum: '$totalMessages' },
                }
            }
        ]);

        // Get contacts by journey stage
        const journeyStages = await Contact.aggregate([
            { $match: { business: businessId } },
            {
                $group: {
                    _id: '$journey.stage',
                    count: { $sum: 1 }
                }
            }
        ]);

        // Get top tags
        const topTags = await Contact.aggregate([
            { $match: { business: businessId } },
            { $unwind: '$tags' },
            {
                $group: {
                    _id: '$tags',
                    count: { $sum: 1 }
                }
            },
            { $sort: { count: -1 } },
            { $limit: 10 }
        ]);

        res.json({
            overview: analytics[0] || {
                totalContacts: 0,
                activeContacts: 0,
                blockedContacts: 0,
                unsubscribedContacts: 0,
                averageEngagementScore: 0,
                totalMessages: 0,
            },
            journeyStages: journeyStages.reduce((acc, stage) => {
                acc[stage._id] = stage.count;
                return acc;
            }, {}),
            topTags: topTags.map(tag => ({
                name: tag._id,
                count: tag.count,
            })),
        });
    } catch (error) {
        logger.business.apiError(req.business._id, 'GET /contacts/analytics', error);
        res.status(500).json({
            error: 'Failed to get contact analytics',
        });
    }
};

module.exports = {
    getContacts,
    getContact,
    createContact,
    updateContact,
    deleteContact,
    addNote,
    bulkUpdateContacts,
    importContacts,
    exportContacts,
    getContactAnalytics,
};
