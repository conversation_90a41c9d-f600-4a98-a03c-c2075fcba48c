const { validationResult } = require('express-validator');
const { Conversation, Contact, Business } = require('../models');
const whatsappService = require('../services/whatsappService');
const aiService = require('../services/aiService');
const logger = require('../services/logger');

// Get all conversations for a business
const getConversations = async (req, res) => {
    try {
        const businessId = req.business._id;
        const {
            page = 1,
            limit = 20,
            status,
            assignedTo,
            priority,
            search,
            sortBy = 'updatedAt',
            sortOrder = 'desc',
        } = req.query;

        // Build query
        const query = { business: businessId };

        if (status) query.status = status;
        if (assignedTo) query.assignedTo = assignedTo;
        if (priority) query.priority = priority;

        // Search in contact names or message content
        if (search) {
            const contacts = await Contact.find({
                business: businessId,
                $or: [
                    { name: { $regex: search, $options: 'i' } },
                    { phone: { $regex: search, $options: 'i' } },
                ],
            }).select('_id');

            query.$or = [
                { contact: { $in: contacts.map(c => c._id) } },
                { 'messages.content.text': { $regex: search, $options: 'i' } },
            ];
        }

        // Pagination
        const skip = (parseInt(page) - 1) * parseInt(limit);
        const sortOptions = {};
        sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

        // Execute query
        const [conversations, totalCount] = await Promise.all([
            Conversation.find(query)
                .sort(sortOptions)
                .skip(skip)
                .limit(parseInt(limit))
                .populate('contact', 'name phone avatar whatsappId')
                .populate('assignedTo', 'firstName lastName avatar')
                .select('-messages'), // Exclude messages for list view
            Conversation.countDocuments(query),
        ]);

        // Add last message and unread count to each conversation
        const conversationsWithDetails = await Promise.all(
            conversations.map(async (conv) => {
                const fullConv = await Conversation.findById(conv._id);
                return {
                    ...conv.toObject(),
                    lastMessage: fullConv.lastMessage,
                    unreadCount: fullConv.unreadCount,
                };
            })
        );

        const totalPages = Math.ceil(totalCount / parseInt(limit));

        res.json({
            conversations: conversationsWithDetails,
            pagination: {
                currentPage: parseInt(page),
                totalPages,
                totalCount,
                hasNextPage: parseInt(page) < totalPages,
                hasPrevPage: parseInt(page) > 1,
                limit: parseInt(limit),
            },
        });
    } catch (error) {
        logger.business.apiError(req.business._id, 'GET /conversations', error);
        res.status(500).json({
            error: 'Failed to fetch conversations',
        });
    }
};

// Get single conversation with messages
const getConversation = async (req, res) => {
    try {
        const { conversationId } = req.params;
        const businessId = req.business._id;

        const conversation = await Conversation.findOne({
            _id: conversationId,
            business: businessId,
        })
            .populate('contact', 'name phone email avatar whatsappId tags journey engagement')
            .populate('assignedTo', 'firstName lastName avatar email')
            .populate('messages.sentBy', 'firstName lastName avatar');

        if (!conversation) {
            return res.status(404).json({
                error: 'Conversation not found',
            });
        }

        // Mark messages as read
        await conversation.markAsRead();

        res.json({ conversation });
    } catch (error) {
        logger.business.apiError(req.business._id, 'GET /conversations/:id', error);
        res.status(500).json({
            error: 'Failed to fetch conversation',
        });
    }
};

// Send a message
const sendMessage = async (req, res) => {
    try {
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                error: 'Validation failed',
                details: errors.array(),
            });
        }

        const { conversationId } = req.params;
        const { type, content } = req.body;
        const businessId = req.business._id;

        // Check if business has reached monthly message limit
        if (req.business.hasReachedLimit('monthlyMessages')) {
            return res.status(403).json({
                error: 'Monthly message limit reached. Please upgrade your plan.',
            });
        }

        const conversation = await Conversation.findOne({
            _id: conversationId,
            business: businessId,
        }).populate('contact');

        if (!conversation) {
            return res.status(404).json({
                error: 'Conversation not found',
            });
        }

        const business = await Business.findById(businessId);
        const contact = conversation.contact;

        let messageResult;

        try {
            // Send message via WhatsApp Business API
            switch (type) {
                case 'text':
                    messageResult = await whatsappService.sendTextMessage(
                        businessId,
                        contact.whatsappId,
                        content.text,
                        business.whatsappConfig.accessToken,
                        business.whatsappConfig.phoneNumberId
                    );
                    break;

                case 'template':
                    messageResult = await whatsappService.sendTemplateMessage(
                        businessId,
                        contact.whatsappId,
                        content.template.name,
                        content.template.language,
                        content.template.parameters,
                        business.whatsappConfig.accessToken,
                        business.whatsappConfig.phoneNumberId
                    );
                    break;

                case 'image':
                case 'video':
                case 'audio':
                case 'document':
                    messageResult = await whatsappService.sendMediaMessage(
                        businessId,
                        contact.whatsappId,
                        type,
                        content.media.url,
                        content.media.caption,
                        business.whatsappConfig.accessToken,
                        business.whatsappConfig.phoneNumberId
                    );
                    break;

                default:
                    return res.status(400).json({
                        error: 'Unsupported message type',
                    });
            }

            // Add message to conversation
            const messageData = {
                messageId: messageResult.messageId,
                type,
                direction: 'outbound',
                content,
                status: 'sent',
                timestamp: new Date(),
                sentBy: req.user.id,
            };

            await conversation.addMessage(messageData);

            // Update contact's last message timestamp
            contact.lastMessageAt = new Date();
            contact.totalMessages += 1;
            await contact.save();

            // Increment business monthly message usage
            await business.incrementUsage('monthlyMessages');

            logger.business.messageSent(businessId, contact._id, type);

            res.json({
                message: 'Message sent successfully',
                messageId: messageResult.messageId,
                conversation,
            });

        } catch (whatsappError) {
            logger.business.whatsappError(businessId, whatsappError, {
                contactId: contact._id,
                messageType: type,
            });

            // Still add the message to conversation but mark as failed
            const messageData = {
                messageId: `failed_${Date.now()}`,
                type,
                direction: 'outbound',
                content,
                status: 'failed',
                timestamp: new Date(),
                sentBy: req.user.id,
            };

            await conversation.addMessage(messageData);

            return res.status(500).json({
                error: 'Failed to send message via WhatsApp',
                details: whatsappError.message,
            });
        }
    } catch (error) {
        logger.business.apiError(req.business._id, 'POST /conversations/:id/messages', error);
        res.status(500).json({
            error: 'Failed to send message',
        });
    }
};

// Assign conversation to agent
const assignConversation = async (req, res) => {
    try {
        const { conversationId } = req.params;
        const { assignedTo } = req.body;
        const businessId = req.business._id;

        const conversation = await Conversation.findOne({
            _id: conversationId,
            business: businessId,
        });

        if (!conversation) {
            return res.status(404).json({
                error: 'Conversation not found',
            });
        }

        // Verify that the user being assigned belongs to the business
        if (assignedTo) {
            const assignee = await User.findOne({
                _id: assignedTo,
                'businesses.business': businessId,
            });

            if (!assignee) {
                return res.status(400).json({
                    error: 'User is not a member of this business',
                });
            }
        }

        await conversation.assign(assignedTo);

        logger.business.userAction(req.user.id, businessId, 'conversation_assigned', {
            conversationId,
            assignedTo,
        });

        res.json({
            message: 'Conversation assigned successfully',
            conversation,
        });
    } catch (error) {
        logger.business.apiError(req.business._id, 'PUT /conversations/:id/assign', error);
        res.status(500).json({
            error: 'Failed to assign conversation',
        });
    }
};

// Update conversation status
const updateConversationStatus = async (req, res) => {
    try {
        const { conversationId } = req.params;
        const { status, priority, tags, closeReason } = req.body;
        const businessId = req.business._id;

        const conversation = await Conversation.findOne({
            _id: conversationId,
            business: businessId,
        });

        if (!conversation) {
            return res.status(404).json({
                error: 'Conversation not found',
            });
        }

        // Update fields
        if (status) {
            if (status === 'closed') {
                await conversation.close(req.user.id, closeReason);
            } else {
                conversation.status = status;
            }
        }

        if (priority) conversation.priority = priority;
        if (tags) conversation.tags = tags;

        await conversation.save();

        logger.business.userAction(req.user.id, businessId, 'conversation_updated', {
            conversationId,
            updates: { status, priority, tags },
        });

        res.json({
            message: 'Conversation updated successfully',
            conversation,
        });
    } catch (error) {
        logger.business.apiError(req.business._id, 'PUT /conversations/:id', error);
        res.status(500).json({
            error: 'Failed to update conversation',
        });
    }
};

// Get conversation analytics
const getConversationAnalytics = async (req, res) => {
    try {
        const businessId = req.business._id;
        const { startDate, endDate } = req.query;

        const dateFilter = {};
        if (startDate) dateFilter.$gte = new Date(startDate);
        if (endDate) dateFilter.$lte = new Date(endDate);

        const matchStage = { business: businessId };
        if (Object.keys(dateFilter).length > 0) {
            matchStage.createdAt = dateFilter;
        }

        const analytics = await Conversation.aggregate([
            { $match: matchStage },
            {
                $group: {
                    _id: null,
                    totalConversations: { $sum: 1 },
                    openConversations: {
                        $sum: { $cond: [{ $eq: ['$status', 'open'] }, 1, 0] }
                    },
                    closedConversations: {
                        $sum: { $cond: [{ $eq: ['$status', 'closed'] }, 1, 0] }
                    },
                    averageResponseTime: { $avg: '$metrics.firstResponseTime' },
                    averageResolutionTime: { $avg: '$metrics.resolutionTime' },
                    totalMessages: { $sum: '$metrics.messageCount' },
                }
            }
        ]);

        // Get conversations by status
        const statusBreakdown = await Conversation.aggregate([
            { $match: matchStage },
            {
                $group: {
                    _id: '$status',
                    count: { $sum: 1 }
                }
            }
        ]);

        // Get conversations by priority
        const priorityBreakdown = await Conversation.aggregate([
            { $match: matchStage },
            {
                $group: {
                    _id: '$priority',
                    count: { $sum: 1 }
                }
            }
        ]);

        res.json({
            overview: analytics[0] || {
                totalConversations: 0,
                openConversations: 0,
                closedConversations: 0,
                averageResponseTime: 0,
                averageResolutionTime: 0,
                totalMessages: 0,
            },
            statusBreakdown: statusBreakdown.reduce((acc, item) => {
                acc[item._id] = item.count;
                return acc;
            }, {}),
            priorityBreakdown: priorityBreakdown.reduce((acc, item) => {
                acc[item._id] = item.count;
                return acc;
            }, {}),
        });
    } catch (error) {
        logger.business.apiError(req.business._id, 'GET /conversations/analytics', error);
        res.status(500).json({
            error: 'Failed to get conversation analytics',
        });
    }
};

// Get AI-powered response suggestions for a conversation
const getAIResponseSuggestions = async (req, res) => {
    try {
        const { conversationId } = req.params;
        const businessId = req.business._id;

        const conversation = await Conversation.findOne({
            _id: conversationId,
            business: businessId,
        }).populate('contact');

        if (!conversation) {
            return res.status(404).json({
                error: 'Conversation not found',
            });
        }

        const business = await Business.findById(businessId);
        const contact = conversation.contact;

        // Generate AI suggestions
        const result = await aiService.generateResponseSuggestions(
            conversation,
            contact,
            {
                name: business.name,
                industry: business.industry,
                settings: business.settings
            }
        );

        if (!result.success) {
            return res.status(500).json({
                error: 'Failed to generate AI suggestions',
                details: result.error,
            });
        }

        logger.business.userAction(req.user.id, businessId, 'ai_suggestions_requested', {
            conversationId,
            suggestionsCount: result.suggestions.length,
        });

        res.json({
            suggestions: result.suggestions,
            confidence: result.confidence,
            model: result.model,
        });
    } catch (error) {
        logger.business.apiError(req.business._id, 'GET /conversations/:id/ai-suggestions', error);
        res.status(500).json({
            error: 'Failed to get AI response suggestions',
        });
    }
};

// Send AI-generated response
const sendAIResponse = async (req, res) => {
    try {
        const { conversationId } = req.params;
        const { useAI = true, customPrompt } = req.body;
        const businessId = req.business._id;

        if (!useAI) {
            return res.status(400).json({
                error: 'AI response generation is disabled',
            });
        }

        const conversation = await Conversation.findOne({
            _id: conversationId,
            business: businessId,
        }).populate('contact');

        if (!conversation) {
            return res.status(404).json({
                error: 'Conversation not found',
            });
        }

        const business = await Business.findById(businessId);
        const contact = conversation.contact;

        // Get the last customer message
        const lastCustomerMessage = conversation.messages
            .filter(msg => msg.direction === 'inbound')
            .pop();

        if (!lastCustomerMessage) {
            return res.status(400).json({
                error: 'No customer message found to respond to',
            });
        }

        // Generate AI response
        const aiResponse = await aiService.generateChatbotResponse(
            lastCustomerMessage,
            contact,
            conversation,
            {
                name: business.name,
                industry: business.industry,
                settings: business.settings,
                customPrompt
            }
        );

        if (!aiResponse.success) {
            return res.status(500).json({
                error: 'Failed to generate AI response',
                details: aiResponse.error,
            });
        }

        // Send the AI-generated response
        try {
            const messageResult = await whatsappService.sendTextMessage(
                businessId,
                contact.whatsappId,
                aiResponse.response,
                business.whatsappConfig.accessToken,
                business.whatsappConfig.phoneNumberId
            );

            // Add message to conversation
            const messageData = {
                messageId: messageResult.messageId,
                type: 'text',
                direction: 'outbound',
                content: { text: aiResponse.response },
                status: 'sent',
                timestamp: new Date(),
                sentBy: req.user.id,
                isAutomated: true,
            };

            await conversation.addMessage(messageData);

            // Update contact's last message timestamp
            contact.lastMessageAt = new Date();
            contact.totalMessages += 1;
            await contact.save();

            logger.business.messageSent(businessId, contact._id, 'text');
            logger.business.userAction(req.user.id, businessId, 'ai_response_sent', {
                conversationId,
                confidence: aiResponse.confidence,
            });

            res.json({
                message: 'AI response sent successfully',
                response: aiResponse.response,
                confidence: aiResponse.confidence,
                messageId: messageResult.messageId,
            });

        } catch (whatsappError) {
            logger.business.whatsappError(businessId, whatsappError, {
                contactId: contact._id,
                messageType: 'ai_response',
            });

            return res.status(500).json({
                error: 'Failed to send AI response via WhatsApp',
                details: whatsappError.message,
                generatedResponse: aiResponse.response,
            });
        }
    } catch (error) {
        logger.business.apiError(req.business._id, 'POST /conversations/:id/ai-response', error);
        res.status(500).json({
            error: 'Failed to send AI response',
        });
    }
};

// Analyze conversation sentiment trends
const analyzeConversationSentiment = async (req, res) => {
    try {
        const { conversationId } = req.params;
        const businessId = req.business._id;

        const conversation = await Conversation.findOne({
            _id: conversationId,
            business: businessId,
        }).populate('contact');

        if (!conversation) {
            return res.status(404).json({
                error: 'Conversation not found',
            });
        }

        // Analyze sentiment of customer messages
        const customerMessages = conversation.messages.filter(
            msg => msg.direction === 'inbound' && msg.content.text
        );

        const sentimentAnalysis = [];

        for (const message of customerMessages.slice(-10)) { // Analyze last 10 messages
            try {
                const result = await aiService.analyzeSentiment(
                    message.content.text,
                    conversation.contact.language || 'en'
                );

                if (result.success) {
                    sentimentAnalysis.push({
                        messageId: message.messageId,
                        timestamp: message.timestamp,
                        text: message.content.text.substring(0, 100), // First 100 chars
                        sentiment: result.sentiment,
                        score: result.score,
                        confidence: result.confidence,
                    });
                }
            } catch (error) {
                logger.error('Error analyzing message sentiment:', error);
            }
        }

        // Calculate overall sentiment trend
        const avgSentiment = sentimentAnalysis.reduce((sum, analysis) =>
            sum + analysis.score, 0) / sentimentAnalysis.length;

        const sentimentTrend = avgSentiment > 0.2 ? 'positive' :
                             avgSentiment < -0.2 ? 'negative' : 'neutral';

        logger.business.userAction(req.user.id, businessId, 'sentiment_analysis_performed', {
            conversationId,
            sentimentTrend,
            messagesAnalyzed: sentimentAnalysis.length,
        });

        res.json({
            conversationId,
            sentimentAnalysis,
            summary: {
                overallTrend: sentimentTrend,
                averageScore: avgSentiment,
                messagesAnalyzed: sentimentAnalysis.length,
                positiveMessages: sentimentAnalysis.filter(s => s.sentiment === 'positive').length,
                negativeMessages: sentimentAnalysis.filter(s => s.sentiment === 'negative').length,
                neutralMessages: sentimentAnalysis.filter(s => s.sentiment === 'neutral').length,
            },
        });
    } catch (error) {
        logger.business.apiError(req.business._id, 'GET /conversations/:id/sentiment', error);
        res.status(500).json({
            error: 'Failed to analyze conversation sentiment',
        });
    }
};

module.exports = {
    getConversations,
    getConversation,
    sendMessage,
    assignConversation,
    updateConversationStatus,
    getConversationAnalytics,
    getAIResponseSuggestions,
    sendAIResponse,
    analyzeConversationSentiment,
};
