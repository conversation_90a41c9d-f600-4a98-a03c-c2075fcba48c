const { Conversation, Analytics } = require('../models/chat');
const prometheus = require('../services/prometheus');

module.exports = {
    async createMessage(req, res) {
        const { messageCounter, analyticsGauge } = prometheus;

        try {
            const { userId, message } = req.body;

            // Save conversation
            const conversation = new Conversation({
                userId,
                messages: {
                    incoming: message,
                    outgoing: 'Hello! How can I help you today?',
                    timestamp: new Date(),
                },
            });

            await conversation.save();

            // Update analytics
            const analytics = await Analytics.findOneAndUpdate(
                { userId },
                { $inc: { interactionCount: 1 }, $set: { resolutionTime: Date.now() } },
                { upsert: true, new: true }
            );

            // Increment Prometheus metrics
            messageCounter.inc({ user_id: userId });
            analyticsGauge.set(
                { user_id: userId, metric: 'interaction_count' },
                analytics.interactionCount
            );
            analyticsGauge.set(
                { user_id: userId, metric: 'resolution_time' },
                analytics.resolutionTime
            );

            res.status(201).json({ conversation, analytics });
        } catch (error) {
            console.error(error);
            res.status(500).send('Server error.');
        }
    },
};