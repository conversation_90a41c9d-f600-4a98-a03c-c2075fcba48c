const { validationResult } = require('express-validator');
const whatsappService = require('../services/whatsappService');
const logger = require('../services/logger');
const config = require('../config');

// Verify webhook (GET request from WhatsApp)
const verifyWebhook = (req, res) => {
    try {
        const mode = req.query['hub.mode'];
        const token = req.query['hub.verify_token'];
        const challenge = req.query['hub.challenge'];

        // Check if mode and token are correct
        if (mode === 'subscribe' && token === config.whatsapp.webhookVerifyToken) {
            logger.info('Webhook verified successfully');
            res.status(200).send(challenge);
        } else {
            logger.warn('Webhook verification failed', {
                mode,
                token,
                expectedToken: config.whatsapp.webhookVerifyToken,
            });
            res.status(403).send('Forbidden');
        }
    } catch (error) {
        logger.error('Webhook verification error:', error);
        res.status(500).send('Internal Server Error');
    }
};

// Handle incoming webhook (POST request from WhatsApp)
const handleWebhook = async (req, res) => {
    try {
        // Validate request
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            logger.warn('Invalid webhook payload', { errors: errors.array() });
            return res.status(400).json({
                error: 'Invalid payload',
                details: errors.array(),
            });
        }

        const webhookData = req.body;

        // Verify webhook signature for security
        const signature = req.headers['x-hub-signature-256'];
        if (signature) {
            const isValid = whatsappService.verifyWebhookSignature(
                JSON.stringify(req.body),
                signature.replace('sha256=', ''),
                config.whatsapp.webhookVerifyToken
            );

            if (!isValid) {
                logger.security.suspiciousActivity(null, 'invalid_webhook_signature', {
                    signature,
                    ip: req.ip,
                });
                return res.status(401).send('Unauthorized');
            }
        }

        // Process each entry in the webhook
        for (const entry of webhookData.entry) {
            for (const change of entry.changes) {
                if (change.field === 'messages') {
                    await processMessageChange(change.value);
                } else if (change.field === 'message_template_status_update') {
                    await processTemplateStatusUpdate(change.value);
                }
            }
        }

        // Acknowledge receipt
        res.status(200).send('OK');
    } catch (error) {
        logger.error('Webhook processing error:', error);
        res.status(500).send('Internal Server Error');
    }
};

// Process message changes (new messages, status updates)
const processMessageChange = async (value) => {
    try {
        // Handle incoming messages
        if (value.messages) {
            for (const message of value.messages) {
                await whatsappService.processIncomingMessage({ entry: [{ changes: [{ value }] }] });
            }
        }

        // Handle message status updates (delivered, read, failed)
        if (value.statuses) {
            for (const status of value.statuses) {
                await processMessageStatus(status);
            }
        }
    } catch (error) {
        logger.error('Error processing message change:', error);
        throw error;
    }
};

// Process message status updates
const processMessageStatus = async (status) => {
    try {
        const { id: messageId, status: messageStatus, timestamp, recipient_id } = status;

        // Find the conversation containing this message
        const conversation = await Conversation.findOne({
            'messages.messageId': messageId,
        });

        if (!conversation) {
            logger.warn('Conversation not found for message status update', { messageId });
            return;
        }

        // Update message status
        const message = conversation.messages.find(msg => msg.messageId === messageId);
        if (message) {
            message.status = messageStatus;
            await conversation.save();

            logger.info('Message status updated', {
                messageId,
                status: messageStatus,
                conversationId: conversation._id,
            });
        }
    } catch (error) {
        logger.error('Error processing message status:', error);
        throw error;
    }
};

// Process template status updates
const processTemplateStatusUpdate = async (value) => {
    try {
        const { message_template_id, message_template_name, message_template_language, event } = value;

        logger.info('Template status update received', {
            templateId: message_template_id,
            templateName: message_template_name,
            language: message_template_language,
            event,
        });

        // Handle template approval/rejection
        if (event === 'APPROVED') {
            // Template approved - can be used in campaigns
            logger.info('Template approved', { templateName: message_template_name });
        } else if (event === 'REJECTED') {
            // Template rejected - notify business owner
            logger.warn('Template rejected', { templateName: message_template_name });
        }
    } catch (error) {
        logger.error('Error processing template status update:', error);
        throw error;
    }
};

// Test webhook endpoint (for development)
const testWebhook = async (req, res) => {
    try {
        if (config.app.env === 'production') {
            return res.status(404).send('Not Found');
        }

        const { businessId, contactPhone, message } = req.body;

        // Simulate incoming message
        const testWebhookData = {
            entry: [{
                changes: [{
                    value: {
                        messaging_product: 'whatsapp',
                        metadata: {
                            display_phone_number: '1234567890',
                            phone_number_id: 'test_phone_number_id',
                        },
                        contacts: [{
                            profile: {
                                name: 'Test User',
                            },
                            wa_id: contactPhone,
                        }],
                        messages: [{
                            from: contactPhone,
                            id: `test_${Date.now()}`,
                            timestamp: Math.floor(Date.now() / 1000).toString(),
                            text: {
                                body: message,
                            },
                            type: 'text',
                        }],
                    },
                }],
            }],
        };

        await whatsappService.processIncomingMessage(testWebhookData);

        res.json({
            message: 'Test webhook processed successfully',
            data: testWebhookData,
        });
    } catch (error) {
        logger.error('Test webhook error:', error);
        res.status(500).json({
            error: 'Test webhook failed',
            message: error.message,
        });
    }
};

// Get webhook info
const getWebhookInfo = (req, res) => {
    res.json({
        webhookUrl: `${config.app.url}/api/v1/webhook/whatsapp`,
        verifyToken: config.whatsapp.webhookVerifyToken,
        supportedEvents: [
            'messages',
            'message_deliveries',
            'message_reads',
            'message_template_status_update',
        ],
        setup: {
            url: `${config.app.url}/api/v1/webhook/whatsapp`,
            method: 'POST',
            verifyToken: config.whatsapp.webhookVerifyToken,
            fields: 'messages,message_deliveries,message_reads,message_template_status_update',
        },
    });
};

module.exports = {
    verifyWebhook,
    handleWebhook,
    testWebhook,
    getWebhookInfo,
};
