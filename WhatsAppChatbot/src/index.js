const express = require('express');
const mongoose = require('mongoose');
const bodyParser = require('body-parser');
const config = require('./config');
const router = require('./routes');
const { register, app: prometheusApp } = require('./services/prometheus');

const app = express();

app.use(bodyParser.json());
app.use('/api/v1', router);
app.use('/metrics', prometheusApp);

mongoose.connect(config.databaseUri, { useNewUrlParser: true, useUnifiedTopology: true });

const errorHandler = (err, req, res, next) => {
    console.error(err.stack);
    res.status(500).send('Something went wrong!');
};

app.use(errorHandler);

app.listen(3000, () => {
    console.log('Server running on port 3000');
});