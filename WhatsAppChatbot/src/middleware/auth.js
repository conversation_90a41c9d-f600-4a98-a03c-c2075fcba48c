const jwt = require('jsonwebtoken');
const { User } = require('../models');
const config = require('../config');
const logger = require('../services/logger');

// Generate JWT token
const generateToken = (userId) => {
    return jwt.sign({ userId }, config.auth.jwtSecret, {
        expiresIn: config.auth.jwtExpiresIn,
    });
};

// Verify JWT token
const verifyToken = (token) => {
    return jwt.verify(token, config.auth.jwtSecret);
};

// Authentication middleware
const authenticate = async (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return res.status(401).json({
                error: 'Access denied. No token provided.',
            });
        }
        
        const token = authHeader.substring(7); // Remove 'Bearer ' prefix
        const decoded = verifyToken(token);
        
        const user = await User.findById(decoded.userId)
            .populate('businesses.business')
            .select('-password');
        
        if (!user) {
            return res.status(401).json({
                error: 'Invalid token. User not found.',
            });
        }
        
        if (user.status !== 'active') {
            return res.status(401).json({
                error: 'Account is inactive.',
            });
        }
        
        req.user = user;
        next();
    } catch (error) {
        logger.error('Authentication error:', error);
        
        if (error.name === 'JsonWebTokenError') {
            return res.status(401).json({
                error: 'Invalid token.',
            });
        }
        
        if (error.name === 'TokenExpiredError') {
            return res.status(401).json({
                error: 'Token expired.',
            });
        }
        
        res.status(500).json({
            error: 'Authentication failed.',
        });
    }
};

// Authorization middleware for business access
const requireBusinessAccess = (requiredPermission = null) => {
    return async (req, res, next) => {
        try {
            const businessId = req.params.businessId || req.body.businessId || req.query.businessId;
            
            if (!businessId) {
                return res.status(400).json({
                    error: 'Business ID is required.',
                });
            }
            
            // Check if user has access to this business
            const businessMembership = req.user.businesses.find(
                b => b.business._id.toString() === businessId.toString()
            );
            
            if (!businessMembership) {
                return res.status(403).json({
                    error: 'Access denied. You do not have access to this business.',
                });
            }
            
            // Check specific permission if required
            if (requiredPermission && !req.user.hasPermission(businessId, requiredPermission)) {
                return res.status(403).json({
                    error: `Access denied. You do not have the required permission: ${requiredPermission}`,
                });
            }
            
            req.business = businessMembership.business;
            req.userRole = businessMembership.role;
            next();
        } catch (error) {
            logger.error('Business authorization error:', error);
            res.status(500).json({
                error: 'Authorization failed.',
            });
        }
    };
};

// Role-based authorization
const requireRole = (roles) => {
    return (req, res, next) => {
        if (!roles.includes(req.user.role)) {
            return res.status(403).json({
                error: 'Access denied. Insufficient privileges.',
            });
        }
        next();
    };
};

// Business role-based authorization
const requireBusinessRole = (roles) => {
    return (req, res, next) => {
        if (!roles.includes(req.userRole)) {
            return res.status(403).json({
                error: 'Access denied. Insufficient business privileges.',
            });
        }
        next();
    };
};

// Optional authentication (for public endpoints that can benefit from user context)
const optionalAuth = async (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return next();
        }
        
        const token = authHeader.substring(7);
        const decoded = verifyToken(token);
        
        const user = await User.findById(decoded.userId)
            .populate('businesses.business')
            .select('-password');
        
        if (user && user.status === 'active') {
            req.user = user;
        }
        
        next();
    } catch (error) {
        // Ignore authentication errors for optional auth
        next();
    }
};

// Rate limiting by user
const userRateLimit = (maxRequests = 100, windowMs = 15 * 60 * 1000) => {
    const requests = new Map();
    
    return (req, res, next) => {
        const userId = req.user?.id || req.ip;
        const now = Date.now();
        const windowStart = now - windowMs;
        
        // Clean old entries
        if (requests.has(userId)) {
            const userRequests = requests.get(userId).filter(time => time > windowStart);
            requests.set(userId, userRequests);
        } else {
            requests.set(userId, []);
        }
        
        const userRequests = requests.get(userId);
        
        if (userRequests.length >= maxRequests) {
            logger.security.rateLimitExceeded(req.ip, req.path);
            return res.status(429).json({
                error: 'Too many requests. Please try again later.',
                retryAfter: Math.ceil(windowMs / 1000),
            });
        }
        
        userRequests.push(now);
        next();
    };
};

// Validate API key for webhook endpoints
const validateApiKey = (req, res, next) => {
    const apiKey = req.headers['x-api-key'] || req.query.api_key;
    
    if (!apiKey) {
        return res.status(401).json({
            error: 'API key is required.',
        });
    }
    
    // In a real implementation, you would validate against stored API keys
    // For now, we'll use a simple check
    if (apiKey !== process.env.WEBHOOK_API_KEY) {
        return res.status(401).json({
            error: 'Invalid API key.',
        });
    }
    
    next();
};

module.exports = {
    generateToken,
    verifyToken,
    authenticate,
    requireBusinessAccess,
    requireRole,
    requireBusinessRole,
    optionalAuth,
    userRateLimit,
    validateApiKey,
};
