const { body, param, query } = require('express-validator');

// User registration validation
const validateRegistration = [
    body('email')
        .isEmail()
        .normalizeEmail()
        .withMessage('Please provide a valid email address'),
    body('password')
        .isLength({ min: 6 })
        .withMessage('Password must be at least 6 characters long')
        .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
        .withMessage('Password must contain at least one uppercase letter, one lowercase letter, and one number'),
    body('firstName')
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('First name must be between 2 and 50 characters'),
    body('lastName')
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('Last name must be between 2 and 50 characters'),
    body('phone')
        .optional()
        .isMobilePhone()
        .withMessage('Please provide a valid phone number'),
    body('businessName')
        .optional()
        .trim()
        .isLength({ min: 2, max: 100 })
        .withMessage('Business name must be between 2 and 100 characters'),
    body('industry')
        .optional()
        .isIn(['salon', 'duka', 'tour_operator', 'freelancer', 'restaurant', 'retail', 'services', 'healthcare', 'education', 'other'])
        .withMessage('Please select a valid industry'),
];

// User login validation
const validateLogin = [
    body('email')
        .isEmail()
        .normalizeEmail()
        .withMessage('Please provide a valid email address'),
    body('password')
        .notEmpty()
        .withMessage('Password is required'),
];

// Profile update validation
const validateProfileUpdate = [
    body('firstName')
        .optional()
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('First name must be between 2 and 50 characters'),
    body('lastName')
        .optional()
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('Last name must be between 2 and 50 characters'),
    body('phone')
        .optional()
        .isMobilePhone()
        .withMessage('Please provide a valid phone number'),
    body('language')
        .optional()
        .isIn(['en', 'sw'])
        .withMessage('Language must be either "en" or "sw"'),
    body('timezone')
        .optional()
        .isLength({ min: 1 })
        .withMessage('Timezone is required'),
];

// Password change validation
const validatePasswordChange = [
    body('currentPassword')
        .notEmpty()
        .withMessage('Current password is required'),
    body('newPassword')
        .isLength({ min: 6 })
        .withMessage('New password must be at least 6 characters long')
        .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
        .withMessage('New password must contain at least one uppercase letter, one lowercase letter, and one number'),
];

// Business creation validation
const validateBusinessCreation = [
    body('name')
        .trim()
        .isLength({ min: 2, max: 100 })
        .withMessage('Business name must be between 2 and 100 characters'),
    body('industry')
        .isIn(['salon', 'duka', 'tour_operator', 'freelancer', 'restaurant', 'retail', 'services', 'healthcare', 'education', 'other'])
        .withMessage('Please select a valid industry'),
    body('contact.phone')
        .isMobilePhone()
        .withMessage('Please provide a valid phone number'),
    body('contact.whatsappNumber')
        .isMobilePhone()
        .withMessage('Please provide a valid WhatsApp number'),
    body('contact.email')
        .optional()
        .isEmail()
        .normalizeEmail()
        .withMessage('Please provide a valid email address'),
];

// Contact creation validation
const validateContactCreation = [
    body('name')
        .optional()
        .trim()
        .isLength({ min: 1, max: 100 })
        .withMessage('Name must be between 1 and 100 characters'),
    body('phone')
        .isMobilePhone()
        .withMessage('Please provide a valid phone number'),
    body('email')
        .optional()
        .isEmail()
        .normalizeEmail()
        .withMessage('Please provide a valid email address'),
    body('tags')
        .optional()
        .isArray()
        .withMessage('Tags must be an array'),
    body('tags.*')
        .optional()
        .trim()
        .isLength({ min: 1, max: 50 })
        .withMessage('Each tag must be between 1 and 50 characters'),
];

// Campaign creation validation
const validateCampaignCreation = [
    body('name')
        .trim()
        .isLength({ min: 2, max: 100 })
        .withMessage('Campaign name must be between 2 and 100 characters'),
    body('type')
        .isIn(['broadcast', 'drip', 'triggered'])
        .withMessage('Campaign type must be broadcast, drip, or triggered'),
    body('message.type')
        .isIn(['text', 'template', 'media'])
        .withMessage('Message type must be text, template, or media'),
    body('message.content.text')
        .if(body('message.type').equals('text'))
        .notEmpty()
        .withMessage('Text content is required for text messages'),
];

// Message sending validation
const validateMessageSending = [
    body('contactId')
        .isMongoId()
        .withMessage('Please provide a valid contact ID'),
    body('type')
        .isIn(['text', 'image', 'audio', 'video', 'document', 'template'])
        .withMessage('Message type must be text, image, audio, video, document, or template'),
    body('content')
        .notEmpty()
        .withMessage('Message content is required'),
];

// MongoDB ObjectId validation
const validateObjectId = (field) => [
    param(field)
        .isMongoId()
        .withMessage(`${field} must be a valid ID`),
];

// Pagination validation
const validatePagination = [
    query('page')
        .optional()
        .isInt({ min: 1 })
        .withMessage('Page must be a positive integer'),
    query('limit')
        .optional()
        .isInt({ min: 1, max: 100 })
        .withMessage('Limit must be between 1 and 100'),
    query('sort')
        .optional()
        .isIn(['createdAt', '-createdAt', 'name', '-name', 'updatedAt', '-updatedAt'])
        .withMessage('Sort must be a valid field'),
];

// Search validation
const validateSearch = [
    query('q')
        .optional()
        .trim()
        .isLength({ min: 1, max: 100 })
        .withMessage('Search query must be between 1 and 100 characters'),
];

// Date range validation
const validateDateRange = [
    query('startDate')
        .optional()
        .isISO8601()
        .withMessage('Start date must be a valid ISO 8601 date'),
    query('endDate')
        .optional()
        .isISO8601()
        .withMessage('End date must be a valid ISO 8601 date'),
];

// WhatsApp webhook validation
const validateWhatsAppWebhook = [
    body('entry')
        .isArray()
        .withMessage('Entry must be an array'),
    body('entry.*.changes')
        .isArray()
        .withMessage('Changes must be an array'),
];

// AI service validation
const validateSentimentAnalysis = [
    body('text')
        .notEmpty()
        .isLength({ min: 1, max: 1000 })
        .withMessage('Text must be between 1 and 1000 characters'),
    body('language')
        .optional()
        .isIn(['en', 'sw', 'fr', 'ar', 'es', 'pt'])
        .withMessage('Language must be a valid language code'),
];

const validateChatbotRequest = [
    body('conversationId')
        .isMongoId()
        .withMessage('Valid conversation ID is required'),
    body('message')
        .notEmpty()
        .withMessage('Message is required'),
    body('message.content')
        .notEmpty()
        .withMessage('Message content is required'),
];

const validateLanguageDetection = [
    body('text')
        .notEmpty()
        .isLength({ min: 1, max: 500 })
        .withMessage('Text must be between 1 and 500 characters'),
];

const validateBulkAnalysis = [
    body('conversationIds')
        .isArray({ min: 1, max: 50 })
        .withMessage('conversationIds must be an array with 1-50 items'),
    body('conversationIds.*')
        .isMongoId()
        .withMessage('Each conversation ID must be valid'),
    body('analysisType')
        .optional()
        .isIn(['sentiment', 'language', 'insights'])
        .withMessage('Analysis type must be sentiment, language, or insights'),
];

module.exports = {
    validateRegistration,
    validateLogin,
    validateProfileUpdate,
    validatePasswordChange,
    validateBusinessCreation,
    validateContactCreation,
    validateCampaignCreation,
    validateMessageSending,
    validateObjectId,
    validatePagination,
    validateSearch,
    validateDateRange,
    validateWhatsAppWebhook,
    validateSentimentAnalysis,
    validateChatbotRequest,
    validateLanguageDetection,
    validateBulkAnalysis,
};
