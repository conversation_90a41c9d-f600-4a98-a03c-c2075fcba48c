const mongoose = require('mongoose');

const automationSchema = new mongoose.Schema({
    business: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Business',
        required: true,
    },
    name: {
        type: String,
        required: true,
        trim: true,
    },
    description: {
        type: String,
        trim: true,
    },
    type: {
        type: String,
        enum: ['keyword', 'welcome', 'away', 'follow_up', 'workflow'],
        required: true,
    },
    status: {
        type: String,
        enum: ['active', 'inactive', 'draft'],
        default: 'draft',
    },
    priority: {
        type: Number,
        default: 1,
        min: 1,
        max: 10,
    },
    // Trigger conditions
    triggers: {
        keywords: [{
            text: {
                type: String,
                required: true,
            },
            matchType: {
                type: String,
                enum: ['exact', 'contains', 'starts_with', 'ends_with'],
                default: 'contains',
            },
            caseSensitive: {
                type: Boolean,
                default: false,
            },
        }],
        events: [{
            type: String,
            enum: ['message_received', 'contact_created', 'tag_added', 'custom_field_updated'],
        }],
        conditions: [{
            field: String,
            operator: {
                type: String,
                enum: ['equals', 'not_equals', 'contains', 'not_contains', 'greater_than', 'less_than'],
            },
            value: mongoose.Schema.Types.Mixed,
        }],
        timeConditions: {
            businessHoursOnly: {
                type: Boolean,
                default: false,
            },
            delayAfterLastMessage: {
                value: Number,
                unit: {
                    type: String,
                    enum: ['minutes', 'hours', 'days'],
                },
            },
        },
    },
    // Actions to perform
    actions: [{
        type: {
            type: String,
            enum: ['send_message', 'add_tag', 'remove_tag', 'assign_agent', 'update_field', 'create_task'],
            required: true,
        },
        delay: {
            value: {
                type: Number,
                default: 0,
            },
            unit: {
                type: String,
                enum: ['seconds', 'minutes', 'hours', 'days'],
                default: 'seconds',
            },
        },
        config: {
            // For send_message action
            message: {
                type: {
                    type: String,
                    enum: ['text', 'template'],
                },
                content: {
                    text: String,
                    template: {
                        name: String,
                        language: String,
                        parameters: [String],
                    },
                },
                translations: {
                    en: String,
                    sw: String,
                },
            },
            // For tag actions
            tags: [String],
            // For assign_agent action
            agentId: {
                type: mongoose.Schema.Types.ObjectId,
                ref: 'User',
            },
            // For update_field action
            field: String,
            value: mongoose.Schema.Types.Mixed,
            // For create_task action
            task: {
                title: String,
                description: String,
                dueDate: Date,
                assignedTo: {
                    type: mongoose.Schema.Types.ObjectId,
                    ref: 'User',
                },
            },
        },
    }],
    // Targeting (who this automation applies to)
    targeting: {
        allContacts: {
            type: Boolean,
            default: true,
        },
        tags: [String],
        segments: [{
            type: mongoose.Schema.Types.ObjectId,
            ref: 'Segment',
        }],
        excludeTags: [String],
        customFilters: [{
            field: String,
            operator: String,
            value: mongoose.Schema.Types.Mixed,
        }],
    },
    // Analytics and tracking
    analytics: {
        triggered: {
            type: Number,
            default: 0,
        },
        completed: {
            type: Number,
            default: 0,
        },
        failed: {
            type: Number,
            default: 0,
        },
        lastTriggered: Date,
        averageCompletionTime: Number,
    },
    // Execution settings
    settings: {
        maxExecutionsPerContact: {
            type: Number,
            default: 1, // How many times this automation can run for the same contact
        },
        cooldownPeriod: {
            value: Number,
            unit: {
                type: String,
                enum: ['minutes', 'hours', 'days'],
            },
        },
        stopOnReply: {
            type: Boolean,
            default: false, // Stop automation if contact replies
        },
        respectBusinessHours: {
            type: Boolean,
            default: true,
        },
    },
    // Execution history
    executions: [{
        contact: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'Contact',
        },
        triggeredAt: {
            type: Date,
            default: Date.now,
        },
        completedAt: Date,
        status: {
            type: String,
            enum: ['running', 'completed', 'failed', 'stopped'],
            default: 'running',
        },
        actions: [{
            type: String,
            executedAt: Date,
            status: {
                type: String,
                enum: ['pending', 'completed', 'failed'],
                default: 'pending',
            },
            error: String,
        }],
    }],
    createdBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true,
    },
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
});

// Indexes
automationSchema.index({ business: 1, status: 1 });
automationSchema.index({ business: 1, type: 1 });
automationSchema.index({ business: 1, priority: -1 });
automationSchema.index({ 'triggers.keywords.text': 1 });

// Virtual for success rate
automationSchema.virtual('successRate').get(function() {
    if (this.analytics.triggered === 0) return 0;
    return ((this.analytics.completed / this.analytics.triggered) * 100).toFixed(2);
});

// Method to check if automation should trigger for a contact
automationSchema.methods.shouldTrigger = function(contact, message, event) {
    // Check if automation is active
    if (this.status !== 'active') return false;

    // Check targeting
    if (!this.targeting.allContacts) {
        // Check tags
        if (this.targeting.tags.length > 0) {
            const hasRequiredTag = this.targeting.tags.some(tag => 
                contact.tags.includes(tag)
            );
            if (!hasRequiredTag) return false;
        }

        // Check excluded tags
        if (this.targeting.excludeTags.length > 0) {
            const hasExcludedTag = this.targeting.excludeTags.some(tag => 
                contact.tags.includes(tag)
            );
            if (hasExcludedTag) return false;
        }
    }

    // Check execution limits
    const contactExecutions = this.executions.filter(
        exec => exec.contact.toString() === contact._id.toString()
    );

    if (contactExecutions.length >= this.settings.maxExecutionsPerContact) {
        return false;
    }

    // Check cooldown period
    if (this.settings.cooldownPeriod && contactExecutions.length > 0) {
        const lastExecution = contactExecutions[contactExecutions.length - 1];
        const cooldownMs = this.settings.cooldownPeriod.value * 
            (this.settings.cooldownPeriod.unit === 'minutes' ? 60000 :
             this.settings.cooldownPeriod.unit === 'hours' ? 3600000 : 86400000);
        
        if (Date.now() - lastExecution.triggeredAt.getTime() < cooldownMs) {
            return false;
        }
    }

    // Check keyword triggers
    if (message && this.triggers.keywords.length > 0) {
        const messageText = message.content.text || '';
        
        return this.triggers.keywords.some(keyword => {
            const text = keyword.caseSensitive ? messageText : messageText.toLowerCase();
            const keywordText = keyword.caseSensitive ? keyword.text : keyword.text.toLowerCase();
            
            switch (keyword.matchType) {
                case 'exact':
                    return text === keywordText;
                case 'contains':
                    return text.includes(keywordText);
                case 'starts_with':
                    return text.startsWith(keywordText);
                case 'ends_with':
                    return text.endsWith(keywordText);
                default:
                    return false;
            }
        });
    }

    // Check event triggers
    if (event && this.triggers.events.includes(event)) {
        return true;
    }

    return false;
};

// Method to execute automation for a contact
automationSchema.methods.execute = async function(contact, triggerData = {}) {
    try {
        // Create execution record
        const execution = {
            contact: contact._id,
            triggeredAt: new Date(),
            status: 'running',
            actions: this.actions.map(action => ({
                type: action.type,
                status: 'pending',
            })),
        };

        this.executions.push(execution);
        this.analytics.triggered += 1;
        this.analytics.lastTriggered = new Date();
        
        await this.save();

        // Execute actions (this would typically be done in a background job)
        // For now, we'll just mark as completed
        execution.status = 'completed';
        execution.completedAt = new Date();
        this.analytics.completed += 1;
        
        await this.save();

        return { success: true, execution };
    } catch (error) {
        this.analytics.failed += 1;
        await this.save();
        throw error;
    }
};

module.exports = mongoose.model('Automation', automationSchema);
