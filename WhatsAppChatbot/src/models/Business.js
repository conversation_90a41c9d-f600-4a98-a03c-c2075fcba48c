const mongoose = require('mongoose');

const businessSchema = new mongoose.Schema({
    name: {
        type: String,
        required: true,
        trim: true,
    },
    description: {
        type: String,
        trim: true,
    },
    industry: {
        type: String,
        enum: [
            'salon', 'duka', 'tour_operator', 'freelancer', 'restaurant',
            'retail', 'services', 'healthcare', 'education', 'other'
        ],
        required: true,
    },
    logo: {
        type: String,
    },
    contact: {
        email: {
            type: String,
            lowercase: true,
            trim: true,
        },
        phone: {
            type: String,
            required: true,
            trim: true,
        },
        whatsappNumber: {
            type: String,
            required: true,
            trim: true,
        },
        website: {
            type: String,
            trim: true,
        },
    },
    address: {
        street: String,
        city: String,
        region: String,
        country: {
            type: String,
            default: 'Tanzania',
        },
        postalCode: String,
        coordinates: {
            latitude: Number,
            longitude: Number,
        },
    },
    whatsappConfig: {
        phoneNumberId: {
            type: String,
            required: true,
        },
        businessAccountId: {
            type: String,
            required: true,
        },
        accessToken: {
            type: String,
            required: true,
        },
        webhookVerifyToken: {
            type: String,
            required: true,
        },
        isConnected: {
            type: Boolean,
            default: false,
        },
        lastConnected: Date,
    },
    subscription: {
        plan: {
            type: String,
            enum: ['free', 'starter', 'professional', 'enterprise'],
            default: 'free',
        },
        status: {
            type: String,
            enum: ['active', 'inactive', 'cancelled', 'past_due'],
            default: 'active',
        },
        stripeCustomerId: String,
        stripeSubscriptionId: String,
        currentPeriodStart: Date,
        currentPeriodEnd: Date,
        cancelAtPeriodEnd: {
            type: Boolean,
            default: false,
        },
    },
    limits: {
        contacts: {
            type: Number,
            default: 100, // Free plan limit
        },
        monthlyMessages: {
            type: Number,
            default: 1000, // Free plan limit
        },
        campaigns: {
            type: Number,
            default: 5, // Free plan limit
        },
        automations: {
            type: Number,
            default: 3, // Free plan limit
        },
        users: {
            type: Number,
            default: 1, // Free plan limit
        },
    },
    usage: {
        contacts: {
            type: Number,
            default: 0,
        },
        monthlyMessages: {
            type: Number,
            default: 0,
        },
        campaigns: {
            type: Number,
            default: 0,
        },
        automations: {
            type: Number,
            default: 0,
        },
        users: {
            type: Number,
            default: 1,
        },
        lastReset: {
            type: Date,
            default: Date.now,
        },
    },
    settings: {
        timezone: {
            type: String,
            default: 'Africa/Dar_es_Salaam',
        },
        currency: {
            type: String,
            default: 'TZS',
        },
        language: {
            type: String,
            enum: ['en', 'sw'],
            default: 'en',
        },
        businessHours: {
            enabled: {
                type: Boolean,
                default: false,
            },
            schedule: {
                monday: { start: '09:00', end: '17:00', enabled: true },
                tuesday: { start: '09:00', end: '17:00', enabled: true },
                wednesday: { start: '09:00', end: '17:00', enabled: true },
                thursday: { start: '09:00', end: '17:00', enabled: true },
                friday: { start: '09:00', end: '17:00', enabled: true },
                saturday: { start: '09:00', end: '13:00', enabled: true },
                sunday: { start: '09:00', end: '13:00', enabled: false },
            },
        },
        autoReply: {
            enabled: {
                type: Boolean,
                default: true,
            },
            message: {
                en: {
                    type: String,
                    default: 'Thank you for contacting us! We will get back to you soon.',
                },
                sw: {
                    type: String,
                    default: 'Asante kwa kuwasiliana nasi! Tutakujibu hivi karibuni.',
                },
            },
        },
        awayMessage: {
            enabled: {
                type: Boolean,
                default: false,
            },
            message: {
                en: {
                    type: String,
                    default: 'We are currently away. We will respond during business hours.',
                },
                sw: {
                    type: String,
                    default: 'Hatuko ofisini kwa sasa. Tutajibu wakati wa kazi.',
                },
            },
        },
    },
    status: {
        type: String,
        enum: ['active', 'inactive', 'suspended'],
        default: 'active',
    },
    owner: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true,
    },
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
});

// Indexes
businessSchema.index({ owner: 1 });
businessSchema.index({ 'whatsappConfig.phoneNumberId': 1 });
businessSchema.index({ 'subscription.status': 1 });
businessSchema.index({ status: 1 });

// Virtual for total team members
businessSchema.virtual('teamMembers', {
    ref: 'User',
    localField: '_id',
    foreignField: 'businesses.business',
    count: true,
});

// Method to check if business has reached limits
businessSchema.methods.hasReachedLimit = function(resource) {
    return this.usage[resource] >= this.limits[resource];
};

// Method to increment usage
businessSchema.methods.incrementUsage = function(resource, amount = 1) {
    this.usage[resource] += amount;
    return this.save();
};

// Method to reset monthly usage
businessSchema.methods.resetMonthlyUsage = function() {
    this.usage.monthlyMessages = 0;
    this.usage.lastReset = new Date();
    return this.save();
};

module.exports = mongoose.model('Business', businessSchema);
