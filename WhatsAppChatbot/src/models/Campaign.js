const mongoose = require('mongoose');

const campaignSchema = new mongoose.Schema({
    business: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Business',
        required: true,
    },
    name: {
        type: String,
        required: true,
        trim: true,
    },
    description: {
        type: String,
        trim: true,
    },
    type: {
        type: String,
        enum: ['broadcast', 'drip', 'triggered'],
        required: true,
    },
    status: {
        type: String,
        enum: ['draft', 'scheduled', 'running', 'paused', 'completed', 'cancelled'],
        default: 'draft',
    },
    // Message content
    message: {
        type: {
            type: String,
            enum: ['text', 'template', 'media'],
            default: 'text',
        },
        content: {
            text: String,
            template: {
                name: String,
                language: String,
                parameters: [mongoose.Schema.Types.Mixed],
            },
            media: {
                type: {
                    type: String,
                    enum: ['image', 'video', 'audio', 'document'],
                },
                url: String,
                caption: String,
                filename: String,
            },
        },
        // Multi-language support
        translations: {
            en: {
                text: String,
                caption: String,
            },
            sw: {
                text: String,
                caption: String,
            },
        },
    },
    // Targeting
    targeting: {
        segments: [{
            type: mongoose.Schema.Types.ObjectId,
            ref: 'Segment',
        }],
        tags: [String],
        customFilters: [{
            field: String,
            operator: {
                type: String,
                enum: ['equals', 'not_equals', 'contains', 'not_contains', 'greater_than', 'less_than'],
            },
            value: mongoose.Schema.Types.Mixed,
        }],
        excludeContacts: [{
            type: mongoose.Schema.Types.ObjectId,
            ref: 'Contact',
        }],
    },
    // Scheduling
    scheduling: {
        type: {
            type: String,
            enum: ['immediate', 'scheduled', 'recurring'],
            default: 'immediate',
        },
        scheduledAt: Date,
        timezone: {
            type: String,
            default: 'Africa/Dar_es_Salaam',
        },
        recurring: {
            frequency: {
                type: String,
                enum: ['daily', 'weekly', 'monthly'],
            },
            interval: Number, // Every X days/weeks/months
            daysOfWeek: [Number], // 0-6 (Sunday-Saturday)
            dayOfMonth: Number, // 1-31
            endDate: Date,
        },
        respectBusinessHours: {
            type: Boolean,
            default: true,
        },
    },
    // Drip campaign settings
    dripSettings: {
        steps: [{
            order: Number,
            delay: {
                value: Number,
                unit: {
                    type: String,
                    enum: ['minutes', 'hours', 'days'],
                },
            },
            message: {
                type: {
                    type: String,
                    enum: ['text', 'template', 'media'],
                },
                content: mongoose.Schema.Types.Mixed,
            },
            conditions: [{
                field: String,
                operator: String,
                value: mongoose.Schema.Types.Mixed,
            }],
        }],
        triggerEvent: {
            type: String,
            enum: ['contact_created', 'tag_added', 'custom_field_updated', 'manual'],
        },
    },
    // Analytics and tracking
    analytics: {
        totalRecipients: {
            type: Number,
            default: 0,
        },
        sent: {
            type: Number,
            default: 0,
        },
        delivered: {
            type: Number,
            default: 0,
        },
        read: {
            type: Number,
            default: 0,
        },
        failed: {
            type: Number,
            default: 0,
        },
        replies: {
            type: Number,
            default: 0,
        },
        clicks: {
            type: Number,
            default: 0,
        },
        conversions: {
            type: Number,
            default: 0,
        },
        unsubscribes: {
            type: Number,
            default: 0,
        },
        // Calculated metrics
        deliveryRate: {
            type: Number,
            default: 0,
        },
        openRate: {
            type: Number,
            default: 0,
        },
        replyRate: {
            type: Number,
            default: 0,
        },
        conversionRate: {
            type: Number,
            default: 0,
        },
        cost: {
            type: Number,
            default: 0,
        },
        roi: {
            type: Number,
            default: 0,
        },
    },
    // Campaign execution
    execution: {
        startedAt: Date,
        completedAt: Date,
        lastRunAt: Date,
        nextRunAt: Date,
        errors: [{
            message: String,
            timestamp: {
                type: Date,
                default: Date.now,
            },
            contactId: {
                type: mongoose.Schema.Types.ObjectId,
                ref: 'Contact',
            },
        }],
    },
    // A/B testing
    abTest: {
        enabled: {
            type: Boolean,
            default: false,
        },
        variants: [{
            name: String,
            percentage: Number,
            message: mongoose.Schema.Types.Mixed,
            analytics: {
                sent: { type: Number, default: 0 },
                delivered: { type: Number, default: 0 },
                read: { type: Number, default: 0 },
                replies: { type: Number, default: 0 },
                conversions: { type: Number, default: 0 },
            },
        }],
        winningVariant: String,
        testDuration: Number, // in hours
        testEndedAt: Date,
    },
    createdBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true,
    },
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
});

// Indexes
campaignSchema.index({ business: 1, status: 1 });
campaignSchema.index({ business: 1, type: 1 });
campaignSchema.index({ business: 1, createdAt: -1 });
campaignSchema.index({ 'scheduling.scheduledAt': 1 });
campaignSchema.index({ 'execution.nextRunAt': 1 });

// Virtual for engagement rate
campaignSchema.virtual('engagementRate').get(function() {
    if (this.analytics.sent === 0) return 0;
    return ((this.analytics.replies + this.analytics.clicks) / this.analytics.sent * 100).toFixed(2);
});

// Method to calculate analytics
campaignSchema.methods.calculateAnalytics = function() {
    if (this.analytics.sent > 0) {
        this.analytics.deliveryRate = (this.analytics.delivered / this.analytics.sent * 100).toFixed(2);
        this.analytics.openRate = (this.analytics.read / this.analytics.delivered * 100).toFixed(2);
        this.analytics.replyRate = (this.analytics.replies / this.analytics.sent * 100).toFixed(2);
        this.analytics.conversionRate = (this.analytics.conversions / this.analytics.sent * 100).toFixed(2);
    }
    return this.save();
};

// Method to start campaign
campaignSchema.methods.start = function() {
    this.status = 'running';
    this.execution.startedAt = new Date();
    return this.save();
};

// Method to pause campaign
campaignSchema.methods.pause = function() {
    this.status = 'paused';
    return this.save();
};

// Method to complete campaign
campaignSchema.methods.complete = function() {
    this.status = 'completed';
    this.execution.completedAt = new Date();
    return this.save();
};

module.exports = mongoose.model('Campaign', campaignSchema);
