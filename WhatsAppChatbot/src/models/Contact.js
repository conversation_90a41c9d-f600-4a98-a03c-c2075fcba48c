const mongoose = require('mongoose');

const contactSchema = new mongoose.Schema({
    business: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Business',
        required: true,
    },
    whatsappId: {
        type: String,
        required: true,
    },
    name: {
        type: String,
        trim: true,
    },
    phone: {
        type: String,
        required: true,
        trim: true,
    },
    email: {
        type: String,
        lowercase: true,
        trim: true,
    },
    avatar: {
        type: String,
    },
    tags: [{
        type: String,
        trim: true,
    }],
    customFields: [{
        name: {
            type: String,
            required: true,
        },
        value: {
            type: mongoose.Schema.Types.Mixed,
        },
        type: {
            type: String,
            enum: ['text', 'number', 'date', 'boolean', 'select'],
            default: 'text',
        },
    }],
    segments: [{
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Segment',
    }],
    status: {
        type: String,
        enum: ['active', 'blocked', 'unsubscribed'],
        default: 'active',
    },
    source: {
        type: String,
        enum: ['whatsapp', 'manual', 'import', 'api', 'website'],
        default: 'whatsapp',
    },
    language: {
        type: String,
        enum: ['en', 'sw', 'auto'],
        default: 'auto',
    },
    lastSeen: {
        type: Date,
    },
    lastMessageAt: {
        type: Date,
    },
    totalMessages: {
        type: Number,
        default: 0,
    },
    notes: [{
        content: {
            type: String,
            required: true,
        },
        createdBy: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User',
            required: true,
        },
        createdAt: {
            type: Date,
            default: Date.now,
        },
    }],
    // Customer journey tracking
    journey: {
        stage: {
            type: String,
            enum: ['lead', 'prospect', 'customer', 'loyal_customer', 'churned'],
            default: 'lead',
        },
        firstContact: {
            type: Date,
            default: Date.now,
        },
        lastPurchase: {
            type: Date,
        },
        totalPurchases: {
            type: Number,
            default: 0,
        },
        totalSpent: {
            type: Number,
            default: 0,
        },
        averageOrderValue: {
            type: Number,
            default: 0,
        },
        lifetimeValue: {
            type: Number,
            default: 0,
        },
    },
    // Engagement metrics
    engagement: {
        score: {
            type: Number,
            default: 0,
            min: 0,
            max: 100,
        },
        lastCalculated: {
            type: Date,
            default: Date.now,
        },
        factors: {
            messageFrequency: Number,
            responseRate: Number,
            purchaseHistory: Number,
            recency: Number,
        },
    },
    // Preferences
    preferences: {
        notifications: {
            type: Boolean,
            default: true,
        },
        marketing: {
            type: Boolean,
            default: true,
        },
        promotions: {
            type: Boolean,
            default: true,
        },
    },
    // Location data
    location: {
        country: String,
        region: String,
        city: String,
        coordinates: {
            latitude: Number,
            longitude: Number,
        },
        timezone: String,
    },
    // Social media profiles
    socialProfiles: {
        facebook: String,
        instagram: String,
        twitter: String,
        linkedin: String,
    },
    // Conversation context
    context: {
        lastIntent: String,
        currentFlow: String,
        variables: {
            type: Map,
            of: mongoose.Schema.Types.Mixed,
        },
    },
    // Assigned agent
    assignedTo: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
    },
    assignedAt: {
        type: Date,
    },
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
});

// Indexes
contactSchema.index({ business: 1, whatsappId: 1 }, { unique: true });
contactSchema.index({ business: 1, phone: 1 });
contactSchema.index({ business: 1, email: 1 });
contactSchema.index({ business: 1, tags: 1 });
contactSchema.index({ business: 1, status: 1 });
contactSchema.index({ business: 1, 'journey.stage': 1 });
contactSchema.index({ business: 1, lastMessageAt: -1 });
contactSchema.index({ assignedTo: 1 });

// Virtual for conversation count
contactSchema.virtual('conversationCount', {
    ref: 'Conversation',
    localField: '_id',
    foreignField: 'contact',
    count: true,
});

// Method to calculate engagement score
contactSchema.methods.calculateEngagementScore = function() {
    const now = new Date();
    const daysSinceLastMessage = this.lastMessageAt ? 
        Math.floor((now - this.lastMessageAt) / (1000 * 60 * 60 * 24)) : 365;
    
    // Recency score (0-25 points)
    const recencyScore = Math.max(0, 25 - (daysSinceLastMessage * 0.5));
    
    // Message frequency score (0-25 points)
    const frequencyScore = Math.min(25, this.totalMessages * 0.5);
    
    // Purchase history score (0-25 points)
    const purchaseScore = Math.min(25, this.journey.totalPurchases * 5);
    
    // Response rate score (0-25 points) - would need to be calculated from conversation data
    const responseScore = 15; // Default value
    
    this.engagement.score = Math.round(recencyScore + frequencyScore + purchaseScore + responseScore);
    this.engagement.lastCalculated = now;
    this.engagement.factors = {
        messageFrequency: frequencyScore,
        responseRate: responseScore,
        purchaseHistory: purchaseScore,
        recency: recencyScore,
    };
    
    return this.engagement.score;
};

// Method to add a note
contactSchema.methods.addNote = function(content, userId) {
    this.notes.push({
        content,
        createdBy: userId,
    });
    return this.save();
};

// Method to update journey stage
contactSchema.methods.updateJourneyStage = function(stage) {
    this.journey.stage = stage;
    return this.save();
};

module.exports = mongoose.model('Contact', contactSchema);
