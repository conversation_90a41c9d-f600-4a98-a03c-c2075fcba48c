const mongoose = require('mongoose');

const messageSchema = new mongoose.Schema({
    messageId: {
        type: String,
        unique: true,
        sparse: true,
    },
    type: {
        type: String,
        enum: ['text', 'image', 'audio', 'video', 'document', 'location', 'contact', 'template'],
        default: 'text',
    },
    direction: {
        type: String,
        enum: ['inbound', 'outbound'],
        required: true,
    },
    content: {
        text: String,
        media: {
            url: String,
            mimeType: String,
            filename: String,
            caption: String,
        },
        location: {
            latitude: Number,
            longitude: Number,
            name: String,
            address: String,
        },
        contact: {
            name: String,
            phone: String,
            email: String,
        },
        template: {
            name: String,
            language: String,
            parameters: [mongoose.Schema.Types.Mixed],
        },
    },
    status: {
        type: String,
        enum: ['sent', 'delivered', 'read', 'failed'],
        default: 'sent',
    },
    timestamp: {
        type: Date,
        default: Date.now,
    },
    sentBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
    },
    // AI-related fields
    intent: {
        name: String,
        confidence: Number,
    },
    entities: [{
        type: String,
        value: String,
        confidence: Number,
    }],
    sentiment: {
        score: Number, // -1 to 1
        label: {
            type: String,
            enum: ['positive', 'neutral', 'negative'],
        },
    },
    // Automation fields
    triggeredAutomation: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Automation',
    },
    isAutomated: {
        type: Boolean,
        default: false,
    },
});

const conversationSchema = new mongoose.Schema({
    business: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Business',
        required: true,
    },
    contact: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Contact',
        required: true,
    },
    whatsappConversationId: {
        type: String,
        unique: true,
        sparse: true,
    },
    status: {
        type: String,
        enum: ['open', 'closed', 'pending', 'resolved'],
        default: 'open',
    },
    priority: {
        type: String,
        enum: ['low', 'medium', 'high', 'urgent'],
        default: 'medium',
    },
    category: {
        type: String,
        enum: ['general', 'support', 'sales', 'complaint', 'inquiry'],
        default: 'general',
    },
    assignedTo: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
    },
    assignedAt: {
        type: Date,
    },
    tags: [{
        type: String,
        trim: true,
    }],
    messages: [messageSchema],
    // Conversation metrics
    metrics: {
        firstResponseTime: Number, // in minutes
        averageResponseTime: Number, // in minutes
        resolutionTime: Number, // in minutes
        messageCount: {
            type: Number,
            default: 0,
        },
        customerSatisfaction: {
            score: Number, // 1-5
            feedback: String,
            submittedAt: Date,
        },
    },
    // Context and state
    context: {
        currentFlow: String,
        variables: {
            type: Map,
            of: mongoose.Schema.Types.Mixed,
        },
        lastIntent: String,
        sessionId: String,
    },
    // Conversation summary (AI-generated)
    summary: {
        content: String,
        generatedAt: Date,
        generatedBy: {
            type: String,
            enum: ['ai', 'agent'],
            default: 'ai',
        },
    },
    // Follow-up information
    followUp: {
        required: {
            type: Boolean,
            default: false,
        },
        scheduledAt: Date,
        assignedTo: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'User',
        },
        notes: String,
        completed: {
            type: Boolean,
            default: false,
        },
        completedAt: Date,
    },
    // Conversation outcome
    outcome: {
        type: String,
        enum: ['resolved', 'escalated', 'converted', 'lost', 'pending'],
    },
    closedAt: {
        type: Date,
    },
    closedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
    },
    closeReason: {
        type: String,
        enum: ['resolved', 'customer_request', 'no_response', 'escalated', 'spam'],
    },
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
});

// Indexes
conversationSchema.index({ business: 1, contact: 1 });
conversationSchema.index({ business: 1, status: 1 });
conversationSchema.index({ business: 1, assignedTo: 1 });
conversationSchema.index({ business: 1, createdAt: -1 });
conversationSchema.index({ business: 1, priority: 1 });
conversationSchema.index({ whatsappConversationId: 1 });
conversationSchema.index({ 'messages.messageId': 1 });

// Virtual for last message
conversationSchema.virtual('lastMessage').get(function() {
    return this.messages.length > 0 ? this.messages[this.messages.length - 1] : null;
});

// Virtual for unread message count
conversationSchema.virtual('unreadCount').get(function() {
    return this.messages.filter(msg => 
        msg.direction === 'inbound' && msg.status !== 'read'
    ).length;
});

// Method to add a message
conversationSchema.methods.addMessage = function(messageData) {
    this.messages.push(messageData);
    this.metrics.messageCount = this.messages.length;
    
    // Update first response time if this is the first outbound message
    if (messageData.direction === 'outbound' && !this.metrics.firstResponseTime) {
        const firstInbound = this.messages.find(msg => msg.direction === 'inbound');
        if (firstInbound) {
            this.metrics.firstResponseTime = Math.round(
                (messageData.timestamp - firstInbound.timestamp) / (1000 * 60)
            );
        }
    }
    
    return this.save();
};

// Method to close conversation
conversationSchema.methods.close = function(userId, reason = 'resolved') {
    this.status = 'closed';
    this.closedAt = new Date();
    this.closedBy = userId;
    this.closeReason = reason;
    
    // Calculate resolution time
    if (this.createdAt) {
        this.metrics.resolutionTime = Math.round(
            (this.closedAt - this.createdAt) / (1000 * 60)
        );
    }
    
    return this.save();
};

// Method to assign conversation
conversationSchema.methods.assign = function(userId) {
    this.assignedTo = userId;
    this.assignedAt = new Date();
    return this.save();
};

// Method to mark messages as read
conversationSchema.methods.markAsRead = function() {
    this.messages.forEach(msg => {
        if (msg.direction === 'inbound' && msg.status !== 'read') {
            msg.status = 'read';
        }
    });
    return this.save();
};

module.exports = mongoose.model('Conversation', conversationSchema);
