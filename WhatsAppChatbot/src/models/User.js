const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

const userSchema = new mongoose.Schema({
    email: {
        type: String,
        required: true,
        unique: true,
        lowercase: true,
        trim: true,
    },
    password: {
        type: String,
        required: true,
        minlength: 6,
    },
    firstName: {
        type: String,
        required: true,
        trim: true,
    },
    lastName: {
        type: String,
        required: true,
        trim: true,
    },
    phone: {
        type: String,
        trim: true,
    },
    avatar: {
        type: String,
    },
    role: {
        type: String,
        enum: ['super_admin', 'admin', 'manager', 'agent', 'user'],
        default: 'user',
    },
    status: {
        type: String,
        enum: ['active', 'inactive', 'suspended'],
        default: 'active',
    },
    language: {
        type: String,
        enum: ['en', 'sw'],
        default: 'en',
    },
    timezone: {
        type: String,
        default: 'Africa/Dar_es_Salaam',
    },
    lastLogin: {
        type: Date,
    },
    emailVerified: {
        type: Boolean,
        default: false,
    },
    emailVerificationToken: {
        type: String,
    },
    passwordResetToken: {
        type: String,
    },
    passwordResetExpires: {
        type: Date,
    },
    // Multi-tenant support
    businesses: [{
        business: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'Business',
        },
        role: {
            type: String,
            enum: ['owner', 'admin', 'manager', 'agent'],
            default: 'agent',
        },
        permissions: [{
            type: String,
            enum: [
                'contacts.read', 'contacts.write', 'contacts.delete',
                'campaigns.read', 'campaigns.write', 'campaigns.delete',
                'analytics.read', 'settings.read', 'settings.write',
                'billing.read', 'billing.write', 'users.read', 'users.write'
            ],
        }],
        joinedAt: {
            type: Date,
            default: Date.now,
        },
    }],
    preferences: {
        notifications: {
            email: {
                type: Boolean,
                default: true,
            },
            whatsapp: {
                type: Boolean,
                default: true,
            },
            newMessages: {
                type: Boolean,
                default: true,
            },
            campaigns: {
                type: Boolean,
                default: true,
            },
        },
        dashboard: {
            defaultView: {
                type: String,
                enum: ['overview', 'contacts', 'campaigns', 'analytics'],
                default: 'overview',
            },
        },
    },
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
});

// Indexes
userSchema.index({ email: 1 });
userSchema.index({ 'businesses.business': 1 });
userSchema.index({ status: 1 });

// Virtual for full name
userSchema.virtual('fullName').get(function() {
    return `${this.firstName} ${this.lastName}`;
});

// Hash password before saving
userSchema.pre('save', async function(next) {
    if (!this.isModified('password')) return next();
    
    this.password = await bcrypt.hash(this.password, 12);
    next();
});

// Compare password method
userSchema.methods.comparePassword = async function(candidatePassword) {
    return await bcrypt.compare(candidatePassword, this.password);
};

// Check if user has permission for a business
userSchema.methods.hasPermission = function(businessId, permission) {
    const businessMembership = this.businesses.find(
        b => b.business.toString() === businessId.toString()
    );
    
    if (!businessMembership) return false;
    
    // Owners and admins have all permissions
    if (['owner', 'admin'].includes(businessMembership.role)) return true;
    
    return businessMembership.permissions.includes(permission);
};

// Get user's role in a specific business
userSchema.methods.getBusinessRole = function(businessId) {
    const businessMembership = this.businesses.find(
        b => b.business.toString() === businessId.toString()
    );
    
    return businessMembership ? businessMembership.role : null;
};

module.exports = mongoose.model('User', userSchema);
