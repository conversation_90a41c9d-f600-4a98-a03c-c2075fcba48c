const mongoose = require('mongoose');

const conversationSchema = new mongoose.Schema({
    userId: String,
    messages: [
        {
            incoming: String,
            outgoing: String,
            timestamp: {
                type: Date,
                default: Date.now,
            },
        },
    ],
});

const analyticsSchema = new mongoose.Schema(
    {
        interactionCount: {
            type: Number,
            default: 0,
        },
        resolutionTime: Number,
    },
    { timestamps: true }
);

module.exports = {
    Conversation: mongoose.model('Conversation', conversationSchema),
    Analytics: mongoose.model('Analytics', analyticsSchema),
};