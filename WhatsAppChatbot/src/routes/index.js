const express = require('express');
const router = express.Router();

// Import controllers
const authController = require('../controllers/authController');
const messageController = require('../controllers/messageController');
const webhookController = require('../controllers/webhookController');
const contactController = require('../controllers/contactController');
const businessController = require('../controllers/businessController');
const conversationController = require('../controllers/conversationController');
const campaignController = require('../controllers/campaignController');
const automationController = require('../controllers/automationController');
const analyticsController = require('../controllers/analyticsController');

// Import middleware
const { authenticate, requireBusinessAccess } = require('../middleware/auth');
const {
    validateRegistration,
    validateLogin,
    validateProfileUpdate,
    validatePasswordChange,
    validateWhatsAppWebhook,
    validateContactCreation,
    validateBusinessCreation,
    validateCampaignCreation,
    validateMessageSending,
    validateObjectId,
    validatePagination,
    validateSearch,
    validateDateRange
} = require('../middleware/validation');

// Welcome route
router.get('/', (req, res) => {
    res.json({
        message: 'Welcome to WhatsApp CRM SaaS API',
        version: '2.0.0',
        documentation: '/api/v1/docs',
        status: 'active',
    });
});

// Authentication routes
router.post('/auth/register', validateRegistration, authController.register);
router.post('/auth/login', validateLogin, authController.login);
router.get('/auth/profile', authenticate, authController.getProfile);
router.put('/auth/profile', authenticate, validateProfileUpdate, authController.updateProfile);
router.put('/auth/password', authenticate, validatePasswordChange, authController.changePassword);
router.post('/auth/logout', authenticate, authController.logout);

// WhatsApp webhook routes
router.get('/webhook/whatsapp', webhookController.verifyWebhook);
router.post('/webhook/whatsapp', validateWhatsAppWebhook, webhookController.handleWebhook);
router.get('/webhook/info', webhookController.getWebhookInfo);

// Development/testing routes
if (process.env.NODE_ENV !== 'production') {
    router.post('/webhook/test', authenticate, webhookController.testWebhook);
}

// Business management routes
router.get('/business/profile', authenticate, requireBusinessAccess(), businessController.getBusinessProfile);
router.put('/business/profile', authenticate, requireBusinessAccess('settings.write'), validateBusinessCreation, businessController.updateBusinessProfile);
router.get('/business/whatsapp-config', authenticate, requireBusinessAccess('settings.read'), businessController.getWhatsAppConfig);
router.put('/business/whatsapp-config', authenticate, requireBusinessAccess('settings.write'), businessController.updateWhatsAppConfig);
router.get('/business/team', authenticate, requireBusinessAccess('users.read'), businessController.getTeamMembers);
router.post('/business/team/invite', authenticate, requireBusinessAccess('users.write'), businessController.inviteTeamMember);
router.put('/business/team/:userId', authenticate, requireBusinessAccess('users.write'), validateObjectId('userId'), businessController.updateTeamMember);
router.delete('/business/team/:userId', authenticate, requireBusinessAccess('users.write'), validateObjectId('userId'), businessController.removeTeamMember);

// Contact management routes
router.get('/contacts', authenticate, requireBusinessAccess('contacts.read'), validatePagination, validateSearch, contactController.getContacts);
router.get('/contacts/analytics', authenticate, requireBusinessAccess('analytics.read'), contactController.getContactAnalytics);
router.get('/contacts/export', authenticate, requireBusinessAccess('contacts.read'), contactController.exportContacts);
router.post('/contacts/import', authenticate, requireBusinessAccess('contacts.write'), contactController.importContacts);
router.put('/contacts/bulk', authenticate, requireBusinessAccess('contacts.write'), contactController.bulkUpdateContacts);
router.get('/contacts/:contactId', authenticate, requireBusinessAccess('contacts.read'), validateObjectId('contactId'), contactController.getContact);
router.post('/contacts', authenticate, requireBusinessAccess('contacts.write'), validateContactCreation, contactController.createContact);
router.put('/contacts/:contactId', authenticate, requireBusinessAccess('contacts.write'), validateObjectId('contactId'), contactController.updateContact);
router.delete('/contacts/:contactId', authenticate, requireBusinessAccess('contacts.delete'), validateObjectId('contactId'), contactController.deleteContact);
router.post('/contacts/:contactId/notes', authenticate, requireBusinessAccess('contacts.write'), validateObjectId('contactId'), contactController.addNote);

// Conversation management routes
router.get('/conversations', authenticate, requireBusinessAccess('contacts.read'), validatePagination, validateSearch, conversationController.getConversations);
router.get('/conversations/analytics', authenticate, requireBusinessAccess('analytics.read'), validateDateRange, conversationController.getConversationAnalytics);
router.get('/conversations/:conversationId', authenticate, requireBusinessAccess('contacts.read'), validateObjectId('conversationId'), conversationController.getConversation);
router.post('/conversations/:conversationId/messages', authenticate, requireBusinessAccess('contacts.write'), validateObjectId('conversationId'), validateMessageSending, conversationController.sendMessage);
router.put('/conversations/:conversationId/assign', authenticate, requireBusinessAccess('contacts.write'), validateObjectId('conversationId'), conversationController.assignConversation);
router.put('/conversations/:conversationId', authenticate, requireBusinessAccess('contacts.write'), validateObjectId('conversationId'), conversationController.updateConversationStatus);

// Campaign management routes
router.get('/campaigns', authenticate, requireBusinessAccess('campaigns.read'), validatePagination, campaignController.getCampaigns);
router.get('/campaigns/:campaignId', authenticate, requireBusinessAccess('campaigns.read'), validateObjectId('campaignId'), campaignController.getCampaign);
router.post('/campaigns', authenticate, requireBusinessAccess('campaigns.write'), validateCampaignCreation, campaignController.createCampaign);
router.put('/campaigns/:campaignId', authenticate, requireBusinessAccess('campaigns.write'), validateObjectId('campaignId'), campaignController.updateCampaign);
router.delete('/campaigns/:campaignId', authenticate, requireBusinessAccess('campaigns.delete'), validateObjectId('campaignId'), campaignController.deleteCampaign);
router.post('/campaigns/:campaignId/start', authenticate, requireBusinessAccess('campaigns.write'), validateObjectId('campaignId'), campaignController.startCampaign);
router.post('/campaigns/:campaignId/pause', authenticate, requireBusinessAccess('campaigns.write'), validateObjectId('campaignId'), campaignController.pauseCampaign);
router.get('/campaigns/:campaignId/analytics', authenticate, requireBusinessAccess('analytics.read'), validateObjectId('campaignId'), campaignController.getCampaignAnalytics);

// Automation management routes
router.get('/automations', authenticate, requireBusinessAccess('campaigns.read'), validatePagination, automationController.getAutomations);
router.get('/automations/:automationId', authenticate, requireBusinessAccess('campaigns.read'), validateObjectId('automationId'), automationController.getAutomation);
router.post('/automations', authenticate, requireBusinessAccess('campaigns.write'), automationController.createAutomation);
router.put('/automations/:automationId', authenticate, requireBusinessAccess('campaigns.write'), validateObjectId('automationId'), automationController.updateAutomation);
router.delete('/automations/:automationId', authenticate, requireBusinessAccess('campaigns.delete'), validateObjectId('automationId'), automationController.deleteAutomation);
router.put('/automations/:automationId/status', authenticate, requireBusinessAccess('campaigns.write'), validateObjectId('automationId'), automationController.toggleAutomationStatus);
router.post('/automations/:automationId/test', authenticate, requireBusinessAccess('campaigns.write'), validateObjectId('automationId'), automationController.testAutomation);
router.get('/automations/:automationId/analytics', authenticate, requireBusinessAccess('analytics.read'), validateObjectId('automationId'), automationController.getAutomationAnalytics);

// Analytics dashboard routes
router.get('/analytics/overview', authenticate, requireBusinessAccess('analytics.read'), analyticsController.getBusinessOverview);
router.get('/analytics/messages', authenticate, requireBusinessAccess('analytics.read'), validateDateRange, analyticsController.getMessageAnalytics);
router.get('/analytics/contacts', authenticate, requireBusinessAccess('analytics.read'), analyticsController.getContactAnalytics);
router.get('/analytics/campaigns', authenticate, requireBusinessAccess('analytics.read'), analyticsController.getCampaignAnalytics);

// Legacy message routes (to be updated)
router.post('/messages', messageController.createMessage);

module.exports = router;