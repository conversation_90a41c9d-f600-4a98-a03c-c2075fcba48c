const express = require('express');
const router = express.Router();

// Import controllers
const authController = require('../controllers/authController');
const messageController = require('../controllers/messageController');
const webhookController = require('../controllers/webhookController');

// Import middleware
const { authenticate, requireBusinessAccess } = require('../middleware/auth');
const {
    validateRegistration,
    validateLogin,
    validateProfileUpdate,
    validatePasswordChange,
    validateWhatsAppWebhook
} = require('../middleware/validation');

// Welcome route
router.get('/', (req, res) => {
    res.json({
        message: 'Welcome to WhatsApp CRM SaaS API',
        version: '2.0.0',
        documentation: '/api/v1/docs',
        status: 'active',
    });
});

// Authentication routes
router.post('/auth/register', validateRegistration, authController.register);
router.post('/auth/login', validateLogin, authController.login);
router.get('/auth/profile', authenticate, authController.getProfile);
router.put('/auth/profile', authenticate, validateProfileUpdate, authController.updateProfile);
router.put('/auth/password', authenticate, validatePasswordChange, authController.changePassword);
router.post('/auth/logout', authenticate, authController.logout);

// WhatsApp webhook routes
router.get('/webhook/whatsapp', webhookController.verifyWebhook);
router.post('/webhook/whatsapp', validateWhatsAppWebhook, webhookController.handleWebhook);
router.get('/webhook/info', webhookController.getWebhookInfo);

// Development/testing routes
if (process.env.NODE_ENV !== 'production') {
    router.post('/webhook/test', authenticate, webhookController.testWebhook);
}

// Legacy message routes (to be updated)
router.post('/messages', messageController.createMessage);

module.exports = router;