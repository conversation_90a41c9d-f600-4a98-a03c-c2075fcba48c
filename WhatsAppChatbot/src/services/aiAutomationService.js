const aiService = require('./aiService');
const whatsappService = require('./whatsappService');
const { Automation, Business, Contact, Conversation } = require('../models');
const logger = require('./logger');

class AIAutomationService {
    // Process incoming message for AI-powered automations
    async processIncomingMessage(business, contact, conversation, message) {
        try {
            // Check if business has AI automations enabled
            if (!business.settings?.aiAutomation?.enabled) {
                return;
            }

            // Get AI-powered automations for this business
            const aiAutomations = await Automation.find({
                business: business._id,
                status: 'active',
                type: { $in: ['ai_response', 'smart_routing', 'sentiment_trigger'] }
            }).sort({ priority: -1 });

            for (const automation of aiAutomations) {
                await this.processAIAutomation(automation, business, contact, conversation, message);
            }
        } catch (error) {
            logger.error('Error processing AI automations:', error);
        }
    }

    // Process individual AI automation
    async processAIAutomation(automation, business, contact, conversation, message) {
        try {
            switch (automation.type) {
                case 'ai_response':
                    await this.handleAIResponse(automation, business, contact, conversation, message);
                    break;
                case 'smart_routing':
                    await this.handleSmartRouting(automation, business, contact, conversation, message);
                    break;
                case 'sentiment_trigger':
                    await this.handleSentimentTrigger(automation, business, contact, conversation, message);
                    break;
            }
        } catch (error) {
            logger.error(`Error processing AI automation ${automation._id}:`, error);
        }
    }

    // Handle AI-powered automatic responses
    async handleAIResponse(automation, business, contact, conversation, message) {
        try {
            // Check if this automation should trigger
            if (!automation.shouldTrigger(contact, message, 'message_received')) {
                return;
            }

            // Check business hours if required
            if (automation.settings.respectBusinessHours && !this.isWithinBusinessHours(business)) {
                return;
            }

            // Generate AI response
            const aiResponse = await aiService.generateChatbotResponse(
                message,
                contact,
                conversation,
                {
                    name: business.name,
                    industry: business.industry,
                    settings: business.settings
                }
            );

            if (!aiResponse.success) {
                logger.warn('AI response generation failed:', aiResponse.error);
                return;
            }

            // Send the AI-generated response
            await this.sendAutomatedResponse(
                business,
                contact,
                conversation,
                aiResponse.response,
                automation._id
            );

            // Log automation execution
            await automation.execute(contact, { message, aiResponse });

            logger.business.automationTriggered(
                business._id,
                automation._id,
                contact._id,
                'ai_response'
            );

        } catch (error) {
            logger.error('Error handling AI response automation:', error);
        }
    }

    // Handle smart routing based on message content and sentiment
    async handleSmartRouting(automation, business, contact, conversation, message) {
        try {
            // Analyze message sentiment and content
            const [sentimentResult, languageResult] = await Promise.all([
                aiService.analyzeSentiment(message.content.text, contact.language || 'en'),
                aiService.detectLanguage(message.content.text)
            ]);

            // Determine routing based on AI analysis
            let routingDecision = null;

            // Route based on sentiment
            if (sentimentResult.success) {
                if (sentimentResult.sentiment === 'negative' && sentimentResult.score < -0.5) {
                    routingDecision = {
                        action: 'escalate',
                        reason: 'negative_sentiment',
                        priority: 'high',
                        department: 'customer_service'
                    };
                } else if (sentimentResult.sentiment === 'positive' && sentimentResult.score > 0.5) {
                    routingDecision = {
                        action: 'route',
                        reason: 'positive_sentiment',
                        priority: 'normal',
                        department: 'sales'
                    };
                }
            }

            // Route based on content analysis (keywords, intent)
            if (!routingDecision) {
                const contentAnalysis = await this.analyzeMessageIntent(message.content.text);
                if (contentAnalysis.intent) {
                    routingDecision = {
                        action: 'route',
                        reason: 'intent_detected',
                        priority: contentAnalysis.priority || 'normal',
                        department: contentAnalysis.department || 'general'
                    };
                }
            }

            // Execute routing decision
            if (routingDecision) {
                await this.executeRouting(conversation, routingDecision, automation);
                
                logger.business.automationTriggered(
                    business._id,
                    automation._id,
                    contact._id,
                    'smart_routing'
                );
            }

        } catch (error) {
            logger.error('Error handling smart routing automation:', error);
        }
    }

    // Handle sentiment-based triggers
    async handleSentimentTrigger(automation, business, contact, conversation, message) {
        try {
            // Analyze sentiment
            const sentimentResult = await aiService.analyzeSentiment(
                message.content.text,
                contact.language || 'en'
            );

            if (!sentimentResult.success) {
                return;
            }

            // Check if sentiment matches trigger conditions
            const triggerConditions = automation.triggers.conditions || [];
            let shouldTrigger = false;

            for (const condition of triggerConditions) {
                if (condition.field === 'sentiment') {
                    switch (condition.operator) {
                        case 'equals':
                            shouldTrigger = sentimentResult.sentiment === condition.value;
                            break;
                        case 'less_than':
                            shouldTrigger = sentimentResult.score < condition.value;
                            break;
                        case 'greater_than':
                            shouldTrigger = sentimentResult.score > condition.value;
                            break;
                    }
                    if (shouldTrigger) break;
                }
            }

            if (!shouldTrigger) {
                return;
            }

            // Execute automation actions
            for (const action of automation.actions) {
                await this.executeAutomationAction(action, business, contact, conversation, automation);
            }

            logger.business.automationTriggered(
                business._id,
                automation._id,
                contact._id,
                'sentiment_trigger'
            );

        } catch (error) {
            logger.error('Error handling sentiment trigger automation:', error);
        }
    }

    // Analyze message intent using AI
    async analyzeMessageIntent(messageText) {
        try {
            const systemPrompt = `Analyze the following customer message and determine the intent and appropriate department routing.

Return a JSON response with:
{
  "intent": "support|sales|billing|general|complaint|inquiry",
  "department": "customer_service|sales|billing|technical|general",
  "priority": "low|normal|high|urgent",
  "confidence": 0.0-1.0
}`;

            const messages = [
                { role: 'system', content: systemPrompt },
                { role: 'user', content: `Customer message: "${messageText}"` }
            ];

            const result = await aiService.callOpenRouter(messages, null, {
                maxTokens: 150,
                temperature: 0.3
            });

            return JSON.parse(result.content);
        } catch (error) {
            logger.error('Error analyzing message intent:', error);
            return { intent: 'general', department: 'general', priority: 'normal', confidence: 0 };
        }
    }

    // Execute routing decision
    async executeRouting(conversation, routingDecision, automation) {
        try {
            // Update conversation priority
            if (routingDecision.priority) {
                conversation.priority = routingDecision.priority;
            }

            // Add routing tag
            if (!conversation.tags.includes(routingDecision.department)) {
                conversation.tags.push(routingDecision.department);
            }

            // If escalation is needed, mark as high priority and add escalation tag
            if (routingDecision.action === 'escalate') {
                conversation.priority = 'urgent';
                conversation.tags.push('escalated');
            }

            await conversation.save();

            // Log routing decision
            logger.info('Smart routing executed:', {
                conversationId: conversation._id,
                decision: routingDecision,
                automationId: automation._id
            });

        } catch (error) {
            logger.error('Error executing routing decision:', error);
        }
    }

    // Execute automation action
    async executeAutomationAction(action, business, contact, conversation, automation) {
        try {
            switch (action.type) {
                case 'send_message':
                    if (action.config.message) {
                        await this.sendAutomatedResponse(
                            business,
                            contact,
                            conversation,
                            action.config.message.content.text,
                            automation._id
                        );
                    }
                    break;

                case 'add_tag':
                    if (action.config.tags) {
                        contact.tags = [...new Set([...contact.tags, ...action.config.tags])];
                        await contact.save();
                    }
                    break;

                case 'assign_agent':
                    if (action.config.agentId) {
                        await conversation.assign(action.config.agentId);
                    }
                    break;

                case 'update_priority':
                    if (action.config.priority) {
                        conversation.priority = action.config.priority;
                        await conversation.save();
                    }
                    break;
            }
        } catch (error) {
            logger.error('Error executing automation action:', error);
        }
    }

    // Send automated response via WhatsApp
    async sendAutomatedResponse(business, contact, conversation, responseText, automationId) {
        try {
            const result = await whatsappService.sendTextMessage(
                business._id,
                contact.whatsappId,
                responseText,
                business.whatsappConfig.accessToken,
                business.whatsappConfig.phoneNumberId
            );

            if (result.success) {
                // Add message to conversation
                const messageData = {
                    messageId: result.messageId,
                    type: 'text',
                    direction: 'outbound',
                    content: { text: responseText },
                    status: 'sent',
                    timestamp: new Date(),
                    isAutomated: true,
                    triggeredAutomation: automationId
                };

                await conversation.addMessage(messageData);

                // Update contact's last message timestamp
                contact.lastMessageAt = new Date();
                await contact.save();

                logger.business.messageSent(business._id, contact._id, 'text', automationId);
            }

            return result;
        } catch (error) {
            logger.error('Error sending automated response:', error);
            throw error;
        }
    }

    // Check if current time is within business hours
    isWithinBusinessHours(business) {
        try {
            const settings = business.settings?.businessHours;
            if (!settings?.enabled) {
                return true; // Always available if business hours not configured
            }

            const now = new Date();
            const currentDay = now.toLocaleDateString('en-US', { weekday: 'lowercase' });
            const currentTime = now.toTimeString().slice(0, 5); // HH:MM format

            const daySchedule = settings.schedule[currentDay];
            if (!daySchedule?.enabled) {
                return false;
            }

            return currentTime >= daySchedule.start && currentTime <= daySchedule.end;
        } catch (error) {
            logger.error('Error checking business hours:', error);
            return true; // Default to available if check fails
        }
    }

    // Generate AI-powered follow-up suggestions
    async generateFollowUpSuggestions(contact, conversations, business) {
        try {
            const contactInsights = await aiService.generateContactInsights(
                contact,
                conversations,
                {
                    name: business.name,
                    industry: business.industry,
                    settings: business.settings
                }
            );

            if (!contactInsights.success) {
                return [];
            }

            return contactInsights.recommendations || [];
        } catch (error) {
            logger.error('Error generating follow-up suggestions:', error);
            return [];
        }
    }
}

module.exports = new AIAutomationService();
