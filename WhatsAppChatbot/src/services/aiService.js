const axios = require('axios');
const config = require('../config');
const logger = require('./logger');

class AIService {
    constructor() {
        this.openrouterConfig = config.ai.openrouter;
        this.maxTokens = config.ai.maxTokens;
        this.temperature = config.ai.temperature;
        this.contextWindow = config.ai.contextWindow;
    }

    // Make API call to OpenRouter
    async callOpenRouter(messages, model = null, options = {}) {
        try {
            const selectedModel = model || this.openrouterConfig.model;
            
            const payload = {
                model: selectedModel,
                messages,
                max_tokens: options.maxTokens || this.maxTokens,
                temperature: options.temperature || this.temperature,
                stream: false,
                ...options
            };

            const response = await axios.post(
                `${this.openrouterConfig.baseUrl}/chat/completions`,
                payload,
                {
                    headers: {
                        'Authorization': `Bearer ${this.openrouterConfig.apiKey}`,
                        'Content-Type': 'application/json',
                        'HTTP-Referer': config.app.url,
                        'X-Title': 'WhatsApp CRM SaaS'
                    },
                    timeout: 30000
                }
            );

            return {
                success: true,
                content: response.data.choices[0].message.content,
                usage: response.data.usage,
                model: response.data.model
            };
        } catch (error) {
            logger.error('OpenRouter API error:', error);
            
            // Try fallback model if primary model fails
            if (model !== this.openrouterConfig.fallbackModel) {
                logger.info('Trying fallback model:', this.openrouterConfig.fallbackModel);
                return await this.callOpenRouter(messages, this.openrouterConfig.fallbackModel, options);
            }
            
            throw new Error(`AI service error: ${error.response?.data?.error?.message || error.message}`);
        }
    }

    // Generate intelligent response suggestions for agents
    async generateResponseSuggestions(conversation, contact, businessContext = {}) {
        try {
            const lastMessages = conversation.messages.slice(-5); // Get last 5 messages for context
            
            const systemPrompt = this.buildSystemPrompt('response_suggestions', {
                businessType: businessContext.industry || 'general business',
                businessName: businessContext.name || 'the business',
                contactName: contact.name || 'the customer',
                language: contact.language || 'en'
            });

            const conversationContext = this.buildConversationContext(lastMessages, contact);
            
            const messages = [
                { role: 'system', content: systemPrompt },
                { role: 'user', content: conversationContext }
            ];

            const result = await this.callOpenRouter(messages, null, {
                maxTokens: 300,
                temperature: 0.8
            });

            // Parse the response to extract multiple suggestions
            const suggestions = this.parseResponseSuggestions(result.content);
            
            return {
                success: true,
                suggestions,
                confidence: this.calculateConfidence(result.usage),
                model: result.model
            };
        } catch (error) {
            logger.error('Error generating response suggestions:', error);
            return {
                success: false,
                error: error.message,
                suggestions: []
            };
        }
    }

    // Analyze sentiment of incoming messages
    async analyzeSentiment(messageText, language = 'en') {
        try {
            const systemPrompt = this.buildSystemPrompt('sentiment_analysis', { language });
            
            const messages = [
                { role: 'system', content: systemPrompt },
                { role: 'user', content: `Analyze the sentiment of this message: "${messageText}"` }
            ];

            const result = await this.callOpenRouter(messages, null, {
                maxTokens: 100,
                temperature: 0.3
            });

            const sentiment = this.parseSentimentResponse(result.content);
            
            return {
                success: true,
                sentiment: sentiment.label,
                score: sentiment.score,
                confidence: sentiment.confidence,
                reasoning: sentiment.reasoning
            };
        } catch (error) {
            logger.error('Error analyzing sentiment:', error);
            return {
                success: false,
                sentiment: 'neutral',
                score: 0,
                confidence: 0,
                error: error.message
            };
        }
    }

    // Generate context-aware chatbot responses
    async generateChatbotResponse(message, contact, conversation, businessContext = {}) {
        try {
            const systemPrompt = this.buildSystemPrompt('chatbot_response', {
                businessType: businessContext.industry || 'general business',
                businessName: businessContext.name || 'our business',
                contactName: contact.name || 'valued customer',
                language: contact.language || 'en',
                businessHours: businessContext.settings?.businessHours || null,
                autoReplyMessage: businessContext.settings?.autoReply?.message || null
            });

            const conversationHistory = this.buildConversationContext(
                conversation.messages.slice(-3), // Last 3 messages for context
                contact
            );

            const messages = [
                { role: 'system', content: systemPrompt },
                { role: 'assistant', content: `Previous conversation: ${conversationHistory}` },
                { role: 'user', content: message.content.text || message.content }
            ];

            const result = await this.callOpenRouter(messages, null, {
                maxTokens: 200,
                temperature: 0.7
            });

            return {
                success: true,
                response: result.content.trim(),
                confidence: this.calculateConfidence(result.usage),
                model: result.model
            };
        } catch (error) {
            logger.error('Error generating chatbot response:', error);
            return {
                success: false,
                response: this.getFallbackResponse(contact.language || 'en'),
                error: error.message
            };
        }
    }

    // Generate AI-powered contact insights and recommendations
    async generateContactInsights(contact, conversations, businessContext = {}) {
        try {
            const contactSummary = this.buildContactSummary(contact, conversations);
            
            const systemPrompt = this.buildSystemPrompt('contact_insights', {
                businessType: businessContext.industry || 'general business',
                language: contact.language || 'en'
            });

            const messages = [
                { role: 'system', content: systemPrompt },
                { role: 'user', content: contactSummary }
            ];

            const result = await this.callOpenRouter(messages, null, {
                maxTokens: 400,
                temperature: 0.6
            });

            const insights = this.parseContactInsights(result.content);
            
            return {
                success: true,
                insights,
                recommendations: insights.recommendations || [],
                riskLevel: insights.riskLevel || 'low',
                nextBestAction: insights.nextBestAction || 'follow_up'
            };
        } catch (error) {
            logger.error('Error generating contact insights:', error);
            return {
                success: false,
                insights: {},
                recommendations: [],
                error: error.message
            };
        }
    }

    // Detect language of incoming message
    async detectLanguage(text) {
        try {
            const systemPrompt = `You are a language detection expert. Analyze the given text and return only the ISO 639-1 language code (e.g., 'en' for English, 'sw' for Swahili). If uncertain, return 'en' as default.`;
            
            const messages = [
                { role: 'system', content: systemPrompt },
                { role: 'user', content: `Detect language: "${text}"` }
            ];

            const result = await this.callOpenRouter(messages, null, {
                maxTokens: 10,
                temperature: 0.1
            });

            const detectedLanguage = result.content.trim().toLowerCase();
            
            // Validate the response is a valid language code
            const validLanguages = ['en', 'sw', 'fr', 'ar', 'es', 'pt'];
            const language = validLanguages.includes(detectedLanguage) ? detectedLanguage : 'en';
            
            return {
                success: true,
                language,
                confidence: this.calculateConfidence(result.usage)
            };
        } catch (error) {
            logger.error('Error detecting language:', error);
            return {
                success: false,
                language: 'en',
                confidence: 0,
                error: error.message
            };
        }
    }

    // Build system prompts for different AI tasks
    buildSystemPrompt(task, context = {}) {
        const baseContext = `You are an AI assistant for a WhatsApp-based CRM system serving East African SMEs. You understand both English and Swahili cultures and business practices.`;
        
        switch (task) {
            case 'response_suggestions':
                return `${baseContext}

Your task is to suggest 3 helpful response options for a customer service agent at ${context.businessName} (${context.businessType}).

Guidelines:
- Responses should be professional yet friendly
- Consider East African business culture and communication style
- Suggest responses in ${context.language === 'sw' ? 'Swahili' : 'English'}
- Include one empathetic response, one solution-focused response, and one follow-up question
- Keep responses concise (1-2 sentences each)
- Format as numbered list: 1. [response] 2. [response] 3. [response]`;

            case 'sentiment_analysis':
                return `${baseContext}

Analyze the sentiment of customer messages with cultural context for East African communication styles.

Return your analysis in this exact JSON format:
{
  "label": "positive|negative|neutral",
  "score": -1.0 to 1.0,
  "confidence": 0.0 to 1.0,
  "reasoning": "brief explanation"
}`;

            case 'chatbot_response':
                const businessHours = context.businessHours ? 
                    `Business hours: ${JSON.stringify(context.businessHours)}` : 
                    'Business hours not specified';
                
                return `${baseContext}

You are responding as ${context.businessName} (${context.businessType}) to ${context.contactName}.

Guidelines:
- Be helpful, professional, and culturally appropriate
- Respond in ${context.language === 'sw' ? 'Swahili' : 'English'}
- ${businessHours}
- If outside business hours, acknowledge and set expectations
- Keep responses concise and actionable
- Use warm, personal tone typical of East African customer service`;

            case 'contact_insights':
                return `${baseContext}

Analyze customer data and provide business insights for a ${context.businessType}.

Provide insights in this JSON format:
{
  "summary": "brief customer summary",
  "engagementLevel": "high|medium|low",
  "riskLevel": "high|medium|low",
  "recommendations": ["action1", "action2", "action3"],
  "nextBestAction": "specific_action",
  "opportunities": ["opportunity1", "opportunity2"]
}`;

            default:
                return baseContext;
        }
    }

    // Build conversation context from messages
    buildConversationContext(messages, contact) {
        const context = messages.map(msg => {
            const sender = msg.direction === 'inbound' ? (contact.name || 'Customer') : 'Agent';
            const content = msg.content.text || '[Media message]';
            return `${sender}: ${content}`;
        }).join('\n');

        return `Contact: ${contact.name || 'Unknown'} (${contact.phone})
Recent conversation:
${context}`;
    }

    // Build contact summary for insights
    buildContactSummary(contact, conversations) {
        const totalConversations = conversations.length;
        const totalMessages = conversations.reduce((sum, conv) => sum + conv.messages.length, 0);
        const avgResponseTime = conversations.reduce((sum, conv) => 
            sum + (conv.metrics.firstResponseTime || 0), 0) / totalConversations;

        return `Contact Profile:
Name: ${contact.name || 'Unknown'}
Phone: ${contact.phone}
Tags: ${contact.tags.join(', ') || 'None'}
Journey Stage: ${contact.journey.stage}
Engagement Score: ${contact.engagement.score}/100
Total Conversations: ${totalConversations}
Total Messages: ${totalMessages}
Average Response Time: ${Math.round(avgResponseTime)} minutes
Last Contact: ${contact.lastMessageAt ? new Date(contact.lastMessageAt).toLocaleDateString() : 'Never'}
Total Purchases: ${contact.journey.totalPurchases}
Lifetime Value: ${contact.journey.lifetimeValue} ${contact.business?.settings?.currency || 'TZS'}`;
    }

    // Parse response suggestions from AI output
    parseResponseSuggestions(content) {
        const lines = content.split('\n').filter(line => line.trim());
        const suggestions = [];
        
        for (const line of lines) {
            const match = line.match(/^\d+\.\s*(.+)$/);
            if (match) {
                suggestions.push({
                    text: match[1].trim(),
                    type: suggestions.length === 0 ? 'empathetic' : 
                          suggestions.length === 1 ? 'solution' : 'question'
                });
            }
        }
        
        // Fallback if parsing fails
        if (suggestions.length === 0) {
            return [
                { text: content.trim(), type: 'general' }
            ];
        }
        
        return suggestions.slice(0, 3); // Max 3 suggestions
    }

    // Parse sentiment analysis response
    parseSentimentResponse(content) {
        try {
            const parsed = JSON.parse(content);
            return {
                label: parsed.label || 'neutral',
                score: parsed.score || 0,
                confidence: parsed.confidence || 0.5,
                reasoning: parsed.reasoning || 'Analysis completed'
            };
        } catch (error) {
            // Fallback parsing
            const lowerContent = content.toLowerCase();
            let label = 'neutral';
            let score = 0;
            
            if (lowerContent.includes('positive') || lowerContent.includes('happy') || lowerContent.includes('satisfied')) {
                label = 'positive';
                score = 0.7;
            } else if (lowerContent.includes('negative') || lowerContent.includes('angry') || lowerContent.includes('frustrated')) {
                label = 'negative';
                score = -0.7;
            }
            
            return { label, score, confidence: 0.5, reasoning: content };
        }
    }

    // Parse contact insights response
    parseContactInsights(content) {
        try {
            return JSON.parse(content);
        } catch (error) {
            // Fallback parsing
            return {
                summary: content.substring(0, 200),
                engagementLevel: 'medium',
                riskLevel: 'low',
                recommendations: ['Follow up with customer', 'Review interaction history'],
                nextBestAction: 'follow_up',
                opportunities: ['Upsell opportunity', 'Referral potential']
            };
        }
    }

    // Calculate confidence based on token usage
    calculateConfidence(usage) {
        if (!usage) return 0.7;
        
        const totalTokens = usage.total_tokens || 0;
        const promptTokens = usage.prompt_tokens || 0;
        const completionTokens = usage.completion_tokens || 0;
        
        // Higher completion tokens relative to prompt usually indicates better response
        const ratio = completionTokens / (promptTokens + 1);
        return Math.min(0.95, Math.max(0.3, ratio * 0.8 + 0.2));
    }

    // Get fallback response when AI fails
    getFallbackResponse(language = 'en') {
        const fallbacks = {
            en: "Thank you for your message. We'll get back to you soon!",
            sw: "Asante kwa ujumbe wako. Tutakujibu hivi karibuni!"
        };
        
        return fallbacks[language] || fallbacks.en;
    }
}

module.exports = new AIService();
