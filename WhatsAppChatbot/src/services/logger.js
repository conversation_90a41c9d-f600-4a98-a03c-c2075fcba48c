const winston = require('winston');
const config = require('../config');

// Define log levels
const levels = {
    error: 0,
    warn: 1,
    info: 2,
    http: 3,
    debug: 4,
};

// Define colors for each level
const colors = {
    error: 'red',
    warn: 'yellow',
    info: 'green',
    http: 'magenta',
    debug: 'white',
};

// Tell winston that you want to link the colors
winston.addColors(colors);

// Define which level to log based on environment
const level = () => {
    const env = config.app.env || 'development';
    const isDevelopment = env === 'development';
    return isDevelopment ? 'debug' : 'warn';
};

// Define different log formats
const developmentFormat = winston.format.combine(
    winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
    winston.format.colorize({ all: true }),
    winston.format.printf(
        (info) => `${info.timestamp} ${info.level}: ${info.message}`
    )
);

const productionFormat = winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
);

// Define transports
const transports = [
    // Console transport
    new winston.transports.Console({
        format: config.app.env === 'development' ? developmentFormat : productionFormat,
    }),
    
    // File transport for errors
    new winston.transports.File({
        filename: 'logs/error.log',
        level: 'error',
        format: productionFormat,
    }),
    
    // File transport for all logs
    new winston.transports.File({
        filename: 'logs/combined.log',
        format: productionFormat,
    }),
];

// Create the logger
const logger = winston.createLogger({
    level: level(),
    levels,
    format: productionFormat,
    transports,
    exitOnError: false,
});

// Create a stream object for Morgan HTTP logging
logger.stream = {
    write: (message) => {
        logger.http(message.trim());
    },
};

// Add request logging middleware
logger.requestLogger = (req, res, next) => {
    const start = Date.now();
    
    res.on('finish', () => {
        const duration = Date.now() - start;
        const logData = {
            method: req.method,
            url: req.url,
            status: res.statusCode,
            duration: `${duration}ms`,
            ip: req.ip,
            userAgent: req.get('User-Agent'),
        };
        
        if (res.statusCode >= 400) {
            logger.warn('HTTP Request', logData);
        } else {
            logger.http('HTTP Request', logData);
        }
    });
    
    next();
};

// Business-specific logging methods
logger.business = {
    messageReceived: (businessId, contactId, messageType) => {
        logger.info('WhatsApp message received', {
            businessId,
            contactId,
            messageType,
            event: 'message_received',
        });
    },
    
    messageSent: (businessId, contactId, messageType, campaignId = null) => {
        logger.info('WhatsApp message sent', {
            businessId,
            contactId,
            messageType,
            campaignId,
            event: 'message_sent',
        });
    },
    
    campaignStarted: (businessId, campaignId, recipientCount) => {
        logger.info('Campaign started', {
            businessId,
            campaignId,
            recipientCount,
            event: 'campaign_started',
        });
    },
    
    automationTriggered: (businessId, automationId, contactId, trigger) => {
        logger.info('Automation triggered', {
            businessId,
            automationId,
            contactId,
            trigger,
            event: 'automation_triggered',
        });
    },
    
    paymentProcessed: (businessId, amount, currency, provider) => {
        logger.info('Payment processed', {
            businessId,
            amount,
            currency,
            provider,
            event: 'payment_processed',
        });
    },
    
    userAction: (userId, businessId, action, details = {}) => {
        logger.info('User action', {
            userId,
            businessId,
            action,
            details,
            event: 'user_action',
        });
    },
    
    apiError: (businessId, endpoint, error, context = {}) => {
        logger.error('API Error', {
            businessId,
            endpoint,
            error: error.message,
            stack: error.stack,
            context,
            event: 'api_error',
        });
    },
    
    whatsappError: (businessId, error, context = {}) => {
        logger.error('WhatsApp API Error', {
            businessId,
            error: error.message,
            context,
            event: 'whatsapp_error',
        });
    },
};

// Security logging
logger.security = {
    loginAttempt: (email, success, ip, userAgent) => {
        const level = success ? 'info' : 'warn';
        logger[level]('Login attempt', {
            email,
            success,
            ip,
            userAgent,
            event: 'login_attempt',
        });
    },
    
    suspiciousActivity: (userId, activity, details) => {
        logger.warn('Suspicious activity detected', {
            userId,
            activity,
            details,
            event: 'suspicious_activity',
        });
    },
    
    rateLimitExceeded: (ip, endpoint) => {
        logger.warn('Rate limit exceeded', {
            ip,
            endpoint,
            event: 'rate_limit_exceeded',
        });
    },
};

module.exports = logger;
