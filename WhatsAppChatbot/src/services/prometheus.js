const client = require('prom-client');
const express = require('express');

const app = express();

const register = new client.Registry();

const messageCounter = new client.Counter({
    name: 'whatsapp_messages_total',
    help: 'Total number of messages received',
    labelNames: ['user_id'],
});

const analyticsGauge = new client.Gauge({
    name: 'whatsapp_analytics',
    help: 'Real-time analytics for WhatsApp chatbot',
    labelNames: ['user_id', 'metric'],
});

app.get('/metrics', async (req, res) => {
    register.metrics().then((metricsString) => {
        res.set('Content-Type', register.contentType);
        res.send(metricsString);
    });
});

module.exports = {
    messageCounter,
    analyticsGauge,
    register,
    app,
};