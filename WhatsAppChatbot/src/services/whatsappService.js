const axios = require('axios');
const config = require('../config');
const logger = require('./logger');
const { Business, Contact, Conversation } = require('../models');

class WhatsAppBusinessAPI {
    constructor() {
        this.baseUrl = config.whatsapp.baseUrl;
        this.apiVersion = config.whatsapp.apiVersion;
    }

    // Get API URL for a specific business
    getApiUrl(phoneNumberId) {
        return `${this.baseUrl}/${this.apiVersion}/${phoneNumberId}`;
    }

    // Get headers for API requests
    getHeaders(accessToken) {
        return {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
        };
    }

    // Send a text message
    async sendTextMessage(businessId, to, text, accessToken, phoneNumberId) {
        try {
            const url = `${this.getApiUrl(phoneNumberId)}/messages`;
            const headers = this.getHeaders(accessToken);

            const payload = {
                messaging_product: 'whatsapp',
                to: to,
                type: 'text',
                text: {
                    body: text,
                },
            };

            const response = await axios.post(url, payload, { headers });

            logger.business.messageSent(businessId, to, 'text');

            return {
                success: true,
                messageId: response.data.messages[0].id,
                data: response.data,
            };
        } catch (error) {
            logger.business.whatsappError(businessId, error, {
                to,
                messageType: 'text',
            });

            throw new Error(`Failed to send text message: ${error.response?.data?.error?.message || error.message}`);
        }
    }

    // Send a template message
    async sendTemplateMessage(businessId, to, templateName, language, parameters, accessToken, phoneNumberId) {
        try {
            const url = `${this.getApiUrl(phoneNumberId)}/messages`;
            const headers = this.getHeaders(accessToken);

            const payload = {
                messaging_product: 'whatsapp',
                to: to,
                type: 'template',
                template: {
                    name: templateName,
                    language: {
                        code: language,
                    },
                    components: parameters ? [{
                        type: 'body',
                        parameters: parameters.map(param => ({
                            type: 'text',
                            text: param,
                        })),
                    }] : [],
                },
            };

            const response = await axios.post(url, payload, { headers });

            logger.business.messageSent(businessId, to, 'template');

            return {
                success: true,
                messageId: response.data.messages[0].id,
                data: response.data,
            };
        } catch (error) {
            logger.business.whatsappError(businessId, error, {
                to,
                messageType: 'template',
                templateName,
            });

            throw new Error(`Failed to send template message: ${error.response?.data?.error?.message || error.message}`);
        }
    }

    // Send a media message (image, video, audio, document)
    async sendMediaMessage(businessId, to, mediaType, mediaUrl, caption, accessToken, phoneNumberId) {
        try {
            const url = `${this.getApiUrl(phoneNumberId)}/messages`;
            const headers = this.getHeaders(accessToken);

            const payload = {
                messaging_product: 'whatsapp',
                to: to,
                type: mediaType,
                [mediaType]: {
                    link: mediaUrl,
                    ...(caption && { caption }),
                },
            };

            const response = await axios.post(url, payload, { headers });

            logger.business.messageSent(businessId, to, mediaType);

            return {
                success: true,
                messageId: response.data.messages[0].id,
                data: response.data,
            };
        } catch (error) {
            logger.business.whatsappError(businessId, error, {
                to,
                messageType: mediaType,
                mediaUrl,
            });

            throw new Error(`Failed to send media message: ${error.response?.data?.error?.message || error.message}`);
        }
    }

    // Mark message as read
    async markMessageAsRead(messageId, accessToken, phoneNumberId) {
        try {
            const url = `${this.getApiUrl(phoneNumberId)}/messages`;
            const headers = this.getHeaders(accessToken);

            const payload = {
                messaging_product: 'whatsapp',
                status: 'read',
                message_id: messageId,
            };

            await axios.post(url, payload, { headers });

            return { success: true };
        } catch (error) {
            logger.error('Failed to mark message as read:', error);
            throw new Error(`Failed to mark message as read: ${error.response?.data?.error?.message || error.message}`);
        }
    }

    // Get media URL
    async getMediaUrl(mediaId, accessToken) {
        try {
            const url = `${this.baseUrl}/${this.apiVersion}/${mediaId}`;
            const headers = this.getHeaders(accessToken);

            const response = await axios.get(url, { headers });

            return {
                success: true,
                url: response.data.url,
                mimeType: response.data.mime_type,
                sha256: response.data.sha256,
                fileSize: response.data.file_size,
            };
        } catch (error) {
            logger.error('Failed to get media URL:', error);
            throw new Error(`Failed to get media URL: ${error.response?.data?.error?.message || error.message}`);
        }
    }

    // Process incoming webhook message
    async processIncomingMessage(webhookData) {
        try {
            const entry = webhookData.entry[0];
            const changes = entry.changes[0];
            const value = changes.value;

            if (!value.messages) {
                return; // Not a message event
            }

            const message = value.messages[0];
            const contact = value.contacts[0];
            const metadata = value.metadata;

            // Find the business by phone number ID
            const business = await Business.findOne({
                'whatsappConfig.phoneNumberId': metadata.phone_number_id,
            });

            if (!business) {
                logger.error('Business not found for phone number ID:', metadata.phone_number_id);
                return;
            }

            // Find or create contact
            let contactRecord = await Contact.findOne({
                business: business._id,
                whatsappId: contact.wa_id,
            });

            if (!contactRecord) {
                contactRecord = new Contact({
                    business: business._id,
                    whatsappId: contact.wa_id,
                    phone: contact.wa_id,
                    name: contact.profile?.name || '',
                    source: 'whatsapp',
                    lastSeen: new Date(),
                });
                await contactRecord.save();

                // Increment contact usage
                await business.incrementUsage('contacts');
            } else {
                contactRecord.lastSeen = new Date();
                contactRecord.lastMessageAt = new Date();
                contactRecord.totalMessages += 1;
                await contactRecord.save();
            }

            // Find or create conversation
            let conversation = await Conversation.findOne({
                business: business._id,
                contact: contactRecord._id,
                status: { $in: ['open', 'pending'] },
            });

            if (!conversation) {
                conversation = new Conversation({
                    business: business._id,
                    contact: contactRecord._id,
                    whatsappConversationId: `${contact.wa_id}_${Date.now()}`,
                    status: 'open',
                });
                await conversation.save();
            }

            // Process message content
            const messageData = {
                messageId: message.id,
                type: message.type,
                direction: 'inbound',
                timestamp: new Date(parseInt(message.timestamp) * 1000),
                status: 'delivered',
            };

            // Extract content based on message type
            switch (message.type) {
                case 'text':
                    messageData.content = { text: message.text.body };
                    break;
                case 'image':
                case 'video':
                case 'audio':
                case 'document':
                    messageData.content = {
                        media: {
                            url: message[message.type].id, // Media ID, will be resolved later
                            mimeType: message[message.type].mime_type,
                            caption: message[message.type].caption || '',
                        },
                    };
                    break;
                case 'location':
                    messageData.content = {
                        location: {
                            latitude: message.location.latitude,
                            longitude: message.location.longitude,
                            name: message.location.name || '',
                            address: message.location.address || '',
                        },
                    };
                    break;
                default:
                    messageData.content = { text: `Unsupported message type: ${message.type}` };
            }

            // Add message to conversation
            await conversation.addMessage(messageData);

            // Increment monthly message usage
            await business.incrementUsage('monthlyMessages');

            logger.business.messageReceived(business._id, contactRecord._id, message.type);

            return {
                business,
                contact: contactRecord,
                conversation,
                message: messageData,
            };
        } catch (error) {
            logger.error('Error processing incoming message:', error);
            throw error;
        }
    }

    // Verify webhook signature (for security)
    verifyWebhookSignature(payload, signature, secret) {
        const crypto = require('crypto');
        const expectedSignature = crypto
            .createHmac('sha256', secret)
            .update(payload)
            .digest('hex');

        return crypto.timingSafeEqual(
            Buffer.from(signature),
            Buffer.from(expectedSignature)
        );
    }
}

module.exports = new WhatsAppBusinessAPI();