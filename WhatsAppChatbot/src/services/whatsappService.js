const { Client } = require('whatsapp-web.js');

const whatsappClient = new Client();

whatsappClient.on('qr', (qr) => {
    console.log('QR RECEIVED:', qr);
});

whatsappClient.on('ready', () => {
    console.log('WhatsApp client is ready!');
});

whatsappClient.on('message', (message) => {
    if (message.body.toLowerCase().includes('hello')) {
        whatsappClient.sendMessage(message.from, 'Hello! How can I help you today?');
    }

    // Logic to handle other messages here
});

whatsappClient.initialize().catch((err) => {
    console.error('Error initializing WhatsApp client:', err);
    process.exit(1);
});

module.exports = whatsappClient;