const mongoose = require('mongoose');
const request = require('supertest');
const app = require('../../src/index');
const { Conversation, Analytics } = require('../../src/models/chat');

describe('End-to-End Testing for Message Controller', () => {
    beforeAll(async () => {
        await mongoose.connect(process.env.DATABASE_URI, { useNewUrlParser: true, useUnifiedTopology: true });
    });

    afterAll(async () => {
        await mongoose.connection.close();
    });

    afterEach(async () => {
        await Conversation.deleteMany({});
        await Analytics.deleteMany({});
    });

    it('should process a message, save conversation, update analytics, and return correct response', async () => {
        const message = {
            userId: '123',
            message: 'Hello! How can I help you today?',
        };

        const res = await request(app)
            .post('/api/v1/messages')
            .send(message);

        expect(res.statusCode).toEqual(201);
        expect(res.body).toHaveProperty('conversation');
        expect(res.body).toHaveProperty('analytics');

        const conversation = await Conversation.findOne({ userId: message.userId });
        const analytics = await Analytics.findOne({ userId: message.userId });

        expect(conversation.messages[0].incoming).toEqual('Hello');
        expect(conversation.messages[0].outgoing).toEqual('Hello! How can I help you today?');

        expect(analytics).toBeTruthy();
        expect(analytics.interactionCount).toEqual(1);
        expect(analytics.resolutionTime).toBeGreaterThan(0);
    });

    it('should return a multi-language response based on user input and selected language', async () => {
        const message = {
            userId: '456',
            message: 'Jambo! Ninayo suala kuhusu bidhaa yako.',
            language: 'sw',
        };

        const res = await request(app)
            .post('/api/v1/messages')
            .send(message);

        expect(res.statusCode).toEqual(201);
        expect(res.body.conversation.messages[0].outgoing).toEqual('Hujambo! Ninaweza kukusaidia?');

        const conversation = await Conversation.findOne({ userId: message.userId });
        expect(conversation.messages[0].incoming).toEqual('Jambo! Ninayo suala kuhusu bidhaa yako.');
        expect(conversation.messages[0].outgoing).toEqual('Hujambo! Ninaweza kukusaidia?');
    });
});