const mongoose = require('mongoose');
const request = require('supertest');
const app = require('../../src/index');
const { Conversation, Analytics } = require('../../src/models/chat');

describe('POST /api/v1/messages', () => {
    beforeAll(async () => {
        await mongoose.connect(process.env.DATABASE_URI, { useNewUrlParser: true, useUnifiedTopology: true });
    });

    afterAll(async () => {
        await mongoose.connection.close();
    });

    afterEach(async () => {
        await Conversation.deleteMany({});
        await Analytics.deleteMany({});
    });

    it('should create a conversation and update analytics', async () => {
        const res = await request(app).post('/api/v1/messages').send({ userId: '123', message: 'Hello' });

        expect(res.statusCode).toEqual(201);
        expect(res.body).toHaveProperty('conversation');
        expect(res.body).toHaveProperty('analytics');

        const conversation = await Conversation.findOne({ userId: '123' });
        const analytics = await Analytics.findOne({ userId: '123' });

        expect(conversation).toBeTruthy();
        expect(analytics).toBeTruthy();
    });

    it('should return 400 if userId is not provided', async () => {
        const res = await request(app).post('/api/v1/messages').send({ message: 'Hello' });

        expect(res.statusCode).toEqual(400);
    });

    it('should return 400 if message is not provided', async () => {
        const res = await request(app).post('/api/v1/messages').send({ userId: '123' });

        expect(res.statusCode).toEqual(400);
    });

    it('should handle server errors gracefully', async () => {
        jest.spyOn(Conversation.prototype, 'save').mockRejectedValueOnce(new Error('Server error'));

        const res = await request(app).post('/api/v1/messages').send({ userId: '123', message: 'Hello' });

        expect(res.statusCode).toEqual(500);
    });
});